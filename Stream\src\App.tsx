import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import About from "./pages/About";
import Services from "./pages/Services";
import CaseStudies from "./pages/CaseStudies";
import Blog from "./pages/Blog";
import Contact from "./pages/Contact";
import Layout from "./components/Layout";
import RequestAccess from "./pages/RequestAccess";
import ClientPortal from "./pages/ClientPortal";
import CompanyPortal from "./pages/CompanyPortal";
import VerificationSuccessPage from "./pages/VerificationSuccessPage";
import { AuthProvider } from "./context/AuthContext";
import "./utils/manualRecovery"; // Load data recovery tools

function App() {
  return (
    <AuthProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/services" element={<Services />} />
            <Route path="/case-studies" element={<CaseStudies />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/request-access" element={<RequestAccess />} />
            <Route path="/client-portal" element={<ClientPortal />} />
            <Route path="/company-portal" element={<CompanyPortal />} />
            <Route path="/verification-success" element={<VerificationSuccessPage />} />
          </Routes>
        </Layout>
      </Router>
    </AuthProvider>
  );
}

export default App;
