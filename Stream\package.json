{"name": "stream-addon-site", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port $PORT"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.5.0"}}