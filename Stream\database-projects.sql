-- Projects system for client portal
-- This adds project-based organization to messages and files

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  client_username text NOT NULL REFERENCES users(username) ON DELETE CASCADE,
  created_by_admin boolean NOT NULL DEFAULT TRUE,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Add project_id to messages table
ALTER TABLE messages ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;

-- Add project_id to files table  
ALTER TABLE files ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;

-- Add client_comment field to files table for client commenting
ALTER TABLE files ADD COLUMN IF NOT EXISTS client_comment text;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_client_username ON projects(client_username);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);
CREATE INDEX IF NOT EXISTS idx_files_project_id ON files(project_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample projects for existing clients (optional)
-- This will help with testing the new project system
INSERT INTO projects (name, description, client_username, created_by_admin, status)
SELECT 
    'General Communication' as name,
    'Default project for general messages and files' as description,
    username as client_username,
    TRUE as created_by_admin,
    'active' as status
FROM users 
WHERE type = 'client'
ON CONFLICT DO NOTHING;

-- Update existing messages to use the default project
UPDATE messages 
SET project_id = (
    SELECT p.id 
    FROM projects p 
    WHERE p.client_username = CASE 
        WHEN messages.sender_username = 'admin' THEN messages.recipient_username
        ELSE messages.sender_username
    END
    AND p.name = 'General Communication'
    LIMIT 1
)
WHERE project_id IS NULL;

-- Update existing files to use the default project  
UPDATE files
SET project_id = (
    SELECT p.id
    FROM projects p
    WHERE p.client_username = CASE
        WHEN files.uploader_username = 'admin' THEN files.target_username
        ELSE files.uploader_username
    END
    AND p.name = 'General Communication'
    LIMIT 1
)
WHERE project_id IS NULL;
