# Access Request System Testing Guide

## Prerequisites
1. Run the SQL script `update_schema_for_username_system.sql` in your Supabase dashboard
2. Ensure the development server is running (`npm run dev`)
3. Verify Supabase configuration in `.env` file

## Test Flow Overview

### 1. Test Request Access Flow
1. Navigate to `http://localhost:5173/client-portal`
2. You should see the login form with username/password fields
3. Click "Request Access" button below the login form
4. Fill out the request form:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Company: "Test Company Inc"
5. Click "Submit Request"
6. Verify success message appears
7. Check Supabase `access_requests` table to confirm the request was saved

### 2. Test Admin Approval Process
1. Navigate to `http://localhost:5173/company-portal`
2. Go to the "Access Requests" section
3. You should see the test request with "pending" status
4. Click "Approve" button
5. Verify:
   - Status changes to "approved"
   - Email client opens with pre-filled approval email
   - Email contains generated username and password
   - Check `access_requests` table for `generated_username` and `generated_password`
   - Check `users` table for new user record

### 3. Test Username-Based Login
1. Go back to `http://localhost:5173/client-portal`
2. Use the generated username and password from the approval email
3. Verify successful login
4. Confirm the portal shows "Welcome, [username]! ([email])"

### 4. Test Rejection Flow
1. Submit another access request (different email)
2. In admin portal, click "Reject" instead of "Approve"
3. Verify status changes to "rejected"
4. Check database for rejection_reason field

### 5. Test Demo Login
1. Use the demo credentials:
   - Username: `demo_user`
   - Password: `SODA`
2. Verify login works and shows correct username

## Expected Database Changes

### access_requests table should have:
- `generated_username` (text, nullable)
- `generated_password` (text, nullable) 
- `rejection_reason` (text, nullable)

### users table should have:
- `username` (text, unique)
- `is_active` (boolean, default true)
- `last_login` (timestamptz, nullable)

### New contact_requests table should exist with all contact form fields

## Username Generation Logic
- Format: `[name_part]_[email_part]_[random]`
- Example: "John Smith" + "<EMAIL>" → `johnsmith_john_a3x`

## Password Generation
- 12 characters with mix of uppercase, lowercase, numbers, and symbols
- Example: `A7k$mN9pQ2x!`

## Email Template
The approval email should contain:
- Portal URL
- Generated username
- Generated password
- Instructions to change password
- <NAME_EMAIL>

## Troubleshooting
- If username generation fails: Check name/email fields for special characters
- If approval fails: Check Supabase logs for constraint violations
- If login fails: Verify username exactly matches generated value
- If email doesn't open: Check browser popup blockers

## Security Notes
- In production, passwords should be hashed before storing
- Consider implementing password reset functionality
- Add rate limiting for access requests
- Implement email verification for requests
