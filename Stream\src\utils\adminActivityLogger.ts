import { supabase } from '../lib/supabaseClient';

export interface AdminActivityData {
  temporaryAccessId: string;
  clientUsername: string;
  adminUsername?: string;
  actionType: string;
  actionDescription: string;
  targetTable?: string;
  targetId?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
}

export class AdminActivityLogger {
  private static instance: AdminActivityLogger;
  private currentAccessId: string | null = null;
  private clientUsername: string | null = null;
  private adminUsername: string | null = null;

  static getInstance(): AdminActivityLogger {
    if (!AdminActivityLogger.instance) {
      AdminActivityLogger.instance = new AdminActivityLogger();
    }
    return AdminActivityLogger.instance;
  }

  // Initialize the logger when admin starts accessing a client account
  initializeSession(accessId: string, clientUsername: string, adminUsername?: string) {
    this.currentAccessId = accessId;
    this.clientUsername = clientUsername;
    this.adminUsername = adminUsername;
    console.log(`📝 Admin activity logging initialized for session ${accessId}`);
  }

  // Clear the session when admin ends access
  clearSession() {
    this.currentAccessId = null;
    this.clientUsername = null;
    this.adminUsername = null;
    console.log('📝 Admin activity logging session cleared');
  }

  // Log an admin activity
  async logActivity(data: Partial<AdminActivityData>): Promise<void> {
    try {
      // Use current session data if not provided
      const activityData: AdminActivityData = {
        temporaryAccessId: data.temporaryAccessId || this.currentAccessId!,
        clientUsername: data.clientUsername || this.clientUsername!,
        adminUsername: data.adminUsername || this.adminUsername,
        actionType: data.actionType!,
        actionDescription: data.actionDescription!,
        targetTable: data.targetTable,
        targetId: data.targetId,
        oldValues: data.oldValues,
        newValues: data.newValues,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent || navigator.userAgent
      };

      // Don't log if we don't have a current session (not admin access)
      if (!this.currentAccessId || !this.clientUsername) {
        return;
      }

      const { error } = await supabase
        .from('admin_activity_log')
        .insert([activityData]);

      if (error) {
        console.error('❌ Failed to log admin activity:', error);
      } else {
        console.log(`📝 Logged admin activity: ${activityData.actionType} - ${activityData.actionDescription}`);
      }

    } catch (error) {
      console.error('❌ Error logging admin activity:', error);
    }
  }

  // Convenience methods for common activities
  async logMessageSent(messageContent: string, messageId?: string) {
    await this.logActivity({
      actionType: 'message_sent',
      actionDescription: `Sent message: "${messageContent.substring(0, 100)}${messageContent.length > 100 ? '...' : ''}"`,
      targetTable: 'messages',
      targetId: messageId,
      newValues: { content: messageContent }
    });
  }

  async logMessageDeleted(messageId: string, messageContent: string) {
    await this.logActivity({
      actionType: 'message_deleted',
      actionDescription: `Deleted message: "${messageContent.substring(0, 100)}${messageContent.length > 100 ? '...' : ''}"`,
      targetTable: 'messages',
      targetId: messageId,
      oldValues: { content: messageContent }
    });
  }

  async logFileUploaded(fileName: string, fileSize: number, fileId?: string) {
    await this.logActivity({
      actionType: 'file_uploaded',
      actionDescription: `Uploaded file: "${fileName}" (${Math.round(fileSize / 1024)}KB)`,
      targetTable: 'files',
      targetId: fileId,
      newValues: { filename: fileName, size: fileSize }
    });
  }

  async logFileDeleted(fileName: string, fileId: string) {
    await this.logActivity({
      actionType: 'file_deleted',
      actionDescription: `Deleted file: "${fileName}"`,
      targetTable: 'files',
      targetId: fileId,
      oldValues: { filename: fileName }
    });
  }

  async logUserUpdated(field: string, oldValue: any, newValue: any) {
    await this.logActivity({
      actionType: 'user_updated',
      actionDescription: `Updated ${field}: "${oldValue}" → "${newValue}"`,
      targetTable: 'users',
      targetId: this.clientUsername!,
      oldValues: { [field]: oldValue },
      newValues: { [field]: newValue }
    });
  }

  async logPasswordChanged() {
    await this.logActivity({
      actionType: 'password_changed',
      actionDescription: 'Changed user password',
      targetTable: 'users',
      targetId: this.clientUsername!
    });
  }

  async logTemporaryAccessGranted(durationDays: number) {
    await this.logActivity({
      actionType: 'temporary_access_granted',
      actionDescription: `Granted temporary access for ${durationDays} days`,
      targetTable: 'temporary_access',
      newValues: { duration_days: durationDays }
    });
  }

  // Get all activities for a specific session
  async getSessionActivities(accessId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('admin_activity_log')
        .select('*')
        .eq('temporary_access_id', accessId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Failed to fetch session activities:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error fetching session activities:', error);
      return [];
    }
  }

  // Format activities for email
  formatActivitiesForEmail(activities: any[]): string {
    if (!activities || activities.length === 0) {
      return 'No changes were made during this session.';
    }

    const grouped = activities.reduce((acc, activity) => {
      const type = activity.action_type;
      if (!acc[type]) acc[type] = [];
      acc[type].push(activity);
      return acc;
    }, {});

    let formatted = '';

    Object.entries(grouped).forEach(([type, acts]: [string, any[]]) => {
      const typeLabel = type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      formatted += `\n${typeLabel}:\n`;
      acts.forEach(activity => {
        const time = new Date(activity.created_at).toLocaleTimeString();
        formatted += `  • ${time}: ${activity.action_description}\n`;
      });
    });

    return formatted;
  }
}

// Export singleton instance
export const adminActivityLogger = AdminActivityLogger.getInstance();
