-- PROTECTED Supabase SQL schema for Stream client/company portal
-- ENHANCED SCHEMA WITH DATA PROTECTION AND BACKUP MECHANISMS
-- Version: 2024-01-30 - Protected Database with Backup System

-- =============================================================================
-- SECURITY AND BACKUP CONFIGURATION
-- =============================================================================

-- Create backup schema for data protection
CREATE SCHEMA IF NOT EXISTS backup_data;

-- Create audit log for tracking all database operations
CREATE TABLE IF NOT EXISTS audit_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  operation_type text NOT NULL, -- 'DELETE', 'TRUNCATE', 'DROP', 'BACKUP', 'RESTORE'
  table_name text NOT NULL,
  affected_rows integer,
  operation_data jsonb, -- Store the actual data being deleted/modified
  performed_by text, -- Who performed the operation
  performed_at timestamptz NOT NULL DEFAULT now(),
  backup_created boolean DEFAULT FALSE, -- Was a backup created before this operation
  backup_location text -- Where the backup was stored
);

-- Drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS create_automatic_backup();
DROP FUNCTION IF EXISTS safe_delete_user_data(text);
DROP FUNCTION IF EXISTS safe_wipe_all_data();

-- Function to create automatic backups before destructive operations
CREATE OR REPLACE FUNCTION create_automatic_backup()
RETURNS void AS $$
DECLARE
  backup_timestamp text;
  table_record record;
BEGIN
  backup_timestamp := to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');
  
  -- Create backup tables for all main tables
  FOR table_record IN 
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN ('audit_log')
  LOOP
    EXECUTE format('CREATE TABLE IF NOT EXISTS backup_data.%I_%s AS SELECT * FROM public.%I',
                   table_record.table_name, backup_timestamp, table_record.table_name);
  END LOOP;
  
  -- Log the backup creation
  INSERT INTO audit_log (operation_type, table_name, performed_by, backup_created, backup_location)
  VALUES ('BACKUP', 'ALL_TABLES', 'SYSTEM', TRUE, 'backup_data schema with timestamp: ' || backup_timestamp);
  
  RAISE NOTICE 'Automatic backup created with timestamp: %', backup_timestamp;
END;
$$ LANGUAGE plpgsql;

-- Function to safely delete user data (with backup)
CREATE OR REPLACE FUNCTION safe_delete_user_data(target_username text)
RETURNS void AS $$
DECLARE
  deleted_messages integer;
  deleted_files integer;
  deleted_projects integer;
  deleted_access integer;
  deleted_user integer;
BEGIN
  -- Create backup before deletion
  PERFORM create_automatic_backup();
  
  -- Count and delete messages
  SELECT COUNT(*) INTO deleted_messages FROM messages 
  WHERE sender_username = target_username OR recipient_username = target_username;
  
  DELETE FROM messages 
  WHERE sender_username = target_username OR recipient_username = target_username;
  
  -- Count and delete files
  SELECT COUNT(*) INTO deleted_files FROM files 
  WHERE uploader_username = target_username OR target_username = target_username;
  
  DELETE FROM files 
  WHERE uploader_username = target_username OR target_username = target_username;
  
  -- Count and delete projects
  SELECT COUNT(*) INTO deleted_projects FROM projects WHERE client_username = target_username;
  DELETE FROM projects WHERE client_username = target_username;
  
  -- Count and delete temporary access
  SELECT COUNT(*) INTO deleted_access FROM temporary_access WHERE client_username = target_username;
  DELETE FROM temporary_access WHERE client_username = target_username;
  
  -- Count and delete user
  SELECT COUNT(*) INTO deleted_user FROM users WHERE username = target_username;
  DELETE FROM users WHERE username = target_username;
  
  -- Log the deletion
  INSERT INTO audit_log (operation_type, table_name, affected_rows, operation_data, performed_by)
  VALUES ('DELETE', 'USER_DATA', 
          deleted_messages + deleted_files + deleted_projects + deleted_access + deleted_user,
          jsonb_build_object(
            'username', target_username,
            'deleted_messages', deleted_messages,
            'deleted_files', deleted_files,
            'deleted_projects', deleted_projects,
            'deleted_access', deleted_access,
            'deleted_user', deleted_user
          ),
          'ADMIN');
          
  RAISE NOTICE 'User data deleted for: % (Messages: %, Files: %, Projects: %, Access: %, User: %)', 
               target_username, deleted_messages, deleted_files, deleted_projects, deleted_access, deleted_user;
END;
$$ LANGUAGE plpgsql;

-- Function to safely wipe all data (with backup and email notification)
CREATE OR REPLACE FUNCTION safe_wipe_all_data()
RETURNS void AS $$
DECLARE
  total_users integer;
  total_messages integer;
  total_files integer;
  backup_timestamp text;
BEGIN
  backup_timestamp := to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');
  
  -- Count existing data
  SELECT COUNT(*) INTO total_users FROM users;
  SELECT COUNT(*) INTO total_messages FROM messages;
  SELECT COUNT(*) INTO total_files FROM files;
  
  -- Create comprehensive backup
  PERFORM create_automatic_backup();
  
  -- Truncate all tables (preserving structure)
  TRUNCATE TABLE removal_requests CASCADE;
  TRUNCATE TABLE admin_activity_log CASCADE;
  TRUNCATE TABLE temporary_access CASCADE;
  TRUNCATE TABLE files CASCADE;
  TRUNCATE TABLE messages CASCADE;
  TRUNCATE TABLE projects CASCADE;
  TRUNCATE TABLE users CASCADE;
  TRUNCATE TABLE contact_requests CASCADE;
  TRUNCATE TABLE access_requests CASCADE;
  
  -- Log the wipe operation
  INSERT INTO audit_log (operation_type, table_name, affected_rows, operation_data, performed_by, backup_created)
  VALUES ('TRUNCATE', 'ALL_TABLES', 
          total_users + total_messages + total_files,
          jsonb_build_object(
            'total_users', total_users,
            'total_messages', total_messages,
            'total_files', total_files,
            'backup_timestamp', backup_timestamp,
            'wipe_timestamp', now()
          ),
          'ADMIN', TRUE);
  
  RAISE NOTICE 'Database wiped successfully. Backup created with timestamp: %', backup_timestamp;
  RAISE NOTICE 'Total data wiped - Users: %, Messages: %, Files: %', total_users, total_messages, total_files;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- MAIN DATABASE SCHEMA (PROTECTED)
-- =============================================================================

-- Table: access_requests (Portal access management)
CREATE TABLE IF NOT EXISTS access_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text NOT NULL,
  status text NOT NULL DEFAULT 'unverified',
  requested_at timestamptz NOT NULL DEFAULT now(),
  approved_at timestamptz NULL,
  approved_by text NULL,
  generated_username text NULL,
  generated_password text NULL,
  rejection_reason text NULL,
  email_verification_token text NULL,
  email_verified_at timestamptz NULL,
  verification_expires_at timestamptz NULL
);

-- Table: users (Approved client portal users with username-based login)
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  username text NOT NULL UNIQUE,
  email text NOT NULL,
  password_hash text NOT NULL,
  name text,
  company text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  last_login timestamptz NULL
);

-- Table: contact_requests (Contact form submissions)
CREATE TABLE IF NOT EXISTS contact_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new',
  submitted_at timestamptz NOT NULL DEFAULT now(),
  responded_at timestamptz NULL,
  responded_by text NULL,
  notes text NULL
);

-- Table: messages (Bidirectional client-admin messaging system)
CREATE TABLE IF NOT EXISTS messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_username text NOT NULL, -- Username of message sender (client username or 'admin')
  recipient_username text NOT NULL, -- Username of message recipient (client username or 'admin')
  content text NOT NULL, -- Message content
  read_by_admin boolean NOT NULL DEFAULT FALSE, -- Has admin seen this message
  read_by_client boolean NOT NULL DEFAULT FALSE, -- Has client seen this message
  deleted_by_sender boolean NOT NULL DEFAULT FALSE, -- Has sender deleted this message from their view
  deleted_by_recipient boolean NOT NULL DEFAULT FALSE, -- Has recipient deleted this message from their view
  project_id uuid, -- Reference to projects table
  created_at timestamptz NOT NULL DEFAULT now() -- Message timestamp
);

-- Table: files (Bidirectional file sharing and management system)
CREATE TABLE IF NOT EXISTS files (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  uploader_username text NOT NULL, -- Username who uploaded the file (client username or 'admin')
  filename text NOT NULL, -- Original filename
  url text NOT NULL, -- File storage URL (placeholder for now)
  file_size integer, -- File size in bytes
  file_type text, -- MIME type of the file
  admin_comment text, -- Admin feedback/comment on client files
  client_comment text, -- Client comment on files
  reviewed_by_admin boolean NOT NULL DEFAULT FALSE, -- Has admin reviewed this file
  uploaded_by_admin boolean NOT NULL DEFAULT FALSE, -- Was this uploaded by admin
  target_username text, -- For admin uploads: which client should see this file
  read_by_client boolean NOT NULL DEFAULT FALSE, -- Has target client seen this file
  deleted_by_uploader boolean NOT NULL DEFAULT FALSE, -- Has uploader deleted this file from their view
  deleted_by_target boolean NOT NULL DEFAULT FALSE, -- Has target user deleted this file from their view
  project_id uuid, -- Reference to projects table
  created_at timestamptz NOT NULL DEFAULT now() -- Upload timestamp
);

-- Table: projects (Project-based organization)
CREATE TABLE IF NOT EXISTS projects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  client_username text NOT NULL,
  created_by_admin boolean NOT NULL DEFAULT TRUE,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Table: temporary_access (Client-granted temporary admin access)
CREATE TABLE IF NOT EXISTS temporary_access (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  client_username text NOT NULL, -- Client granting access
  granted_by_client_at timestamptz NOT NULL DEFAULT now(), -- When client granted access
  expires_at timestamptz NOT NULL, -- When access expires
  access_duration_days integer NOT NULL, -- Duration in days for reference
  status text NOT NULL DEFAULT 'active', -- 'active', 'expired', 'revoked'
  revoked_at timestamptz NULL, -- When access was revoked (if applicable)
  revoked_by text NULL, -- Who revoked access: 'client' or 'admin'
  last_used_at timestamptz NULL, -- When admin last used this access
  access_token text NOT NULL UNIQUE, -- Unique token for this access grant
  admin_currently_accessing boolean NOT NULL DEFAULT FALSE, -- Is admin currently using this access
  admin_session_started_at timestamptz NULL, -- When admin started current session
  admin_session_ended_at timestamptz NULL, -- When admin ended current session
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Table: admin_activity_log (Track what admins do during temporary access sessions)
CREATE TABLE IF NOT EXISTS admin_activity_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temporary_access_id uuid NOT NULL REFERENCES temporary_access(id) ON DELETE CASCADE,
  client_username text NOT NULL, -- Which client's account was affected
  admin_username text, -- Which admin performed the action (if available)
  action_type text NOT NULL, -- 'message_sent', 'message_deleted', 'file_uploaded', 'file_deleted', 'user_updated', 'password_changed', etc.
  action_description text NOT NULL, -- Human-readable description of what was done
  target_table text, -- Which database table was affected (messages, files, users, etc.)
  target_id text, -- ID of the specific record that was affected
  old_values jsonb, -- Previous values before the change (for updates/deletes)
  new_values jsonb, -- New values after the change (for creates/updates)
  ip_address text, -- IP address of the admin (for security)
  user_agent text, -- Browser/device info
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Table: removal_requests (Requests to remove shared messages/files)
CREATE TABLE IF NOT EXISTS removal_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  requester_username text NOT NULL, -- Who is requesting the removal
  target_username text NOT NULL, -- Who needs to approve the removal
  item_type text NOT NULL, -- 'message' or 'file'
  item_id uuid NOT NULL, -- ID of the message or file to be removed
  reason text, -- Optional reason for removal request
  status text NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'denied'
  responded_at timestamptz, -- When the target user responded
  request_count integer NOT NULL DEFAULT 1, -- Track how many times this item has been requested for deletion
  created_at timestamptz NOT NULL DEFAULT now()
);

-- =============================================================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================================================

-- Add foreign key constraints for projects
ALTER TABLE messages ADD CONSTRAINT fk_messages_project_id
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL;

ALTER TABLE files ADD CONSTRAINT fk_files_project_id
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL;

ALTER TABLE projects ADD CONSTRAINT fk_projects_client_username
  FOREIGN KEY (client_username) REFERENCES users(username) ON DELETE CASCADE;

-- =============================================================================
-- PERFORMANCE INDEXES
-- =============================================================================

-- Access requests indexes
CREATE INDEX IF NOT EXISTS idx_access_requests_status ON access_requests(status);
CREATE INDEX IF NOT EXISTS idx_access_requests_email ON access_requests(email);
CREATE INDEX IF NOT EXISTS idx_access_requests_requested_at ON access_requests(requested_at DESC);

-- Users indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Contact requests indexes
CREATE INDEX IF NOT EXISTS idx_contact_requests_status ON contact_requests(status);
CREATE INDEX IF NOT EXISTS idx_contact_requests_submitted_at ON contact_requests(submitted_at DESC);

-- Messages indexes (for conversation queries)
CREATE INDEX IF NOT EXISTS idx_messages_sender_username ON messages(sender_username);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_username ON messages(recipient_username);
CREATE INDEX IF NOT EXISTS idx_messages_read_by_admin ON messages(read_by_admin);
CREATE INDEX IF NOT EXISTS idx_messages_read_by_client ON messages(read_by_client);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(sender_username, recipient_username);
CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);

-- Files indexes (for file management queries)
CREATE INDEX IF NOT EXISTS idx_files_uploader_username ON files(uploader_username);
CREATE INDEX IF NOT EXISTS idx_files_target_username ON files(target_username);
CREATE INDEX IF NOT EXISTS idx_files_reviewed_by_admin ON files(reviewed_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by_admin ON files(uploaded_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_read_by_client ON files(read_by_client);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_admin_uploads ON files(uploaded_by_admin, target_username);
CREATE INDEX IF NOT EXISTS idx_files_project_id ON files(project_id);

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_client_username ON projects(client_username);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);

-- Temporary access indexes
CREATE INDEX IF NOT EXISTS idx_temporary_access_client_username ON temporary_access(client_username);
CREATE INDEX IF NOT EXISTS idx_temporary_access_status ON temporary_access(status);
CREATE INDEX IF NOT EXISTS idx_temporary_access_expires_at ON temporary_access(expires_at);

-- Admin activity log indexes
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_temporary_access_id ON admin_activity_log(temporary_access_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_client_username ON admin_activity_log(client_username);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at DESC);

-- Removal requests indexes
CREATE INDEX IF NOT EXISTS idx_removal_requests_requester_username ON removal_requests(requester_username);
CREATE INDEX IF NOT EXISTS idx_removal_requests_target_username ON removal_requests(target_username);
CREATE INDEX IF NOT EXISTS idx_removal_requests_status ON removal_requests(status);
CREATE INDEX IF NOT EXISTS idx_removal_requests_item_type_id ON removal_requests(item_type, item_id);

-- Deletion tracking indexes
CREATE INDEX IF NOT EXISTS idx_messages_deleted_by_sender ON messages(deleted_by_sender);
CREATE INDEX IF NOT EXISTS idx_messages_deleted_by_recipient ON messages(deleted_by_recipient);
CREATE INDEX IF NOT EXISTS idx_files_deleted_by_uploader ON files(deleted_by_uploader);
CREATE INDEX IF NOT EXISTS idx_files_deleted_by_target ON files(deleted_by_target);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_operation_type ON audit_log(operation_type);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_performed_at ON audit_log(performed_at DESC);

-- =============================================================================
-- TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- EMAIL NOTIFICATION SYSTEM
-- =============================================================================

-- Function to send email notifications for critical operations
CREATE OR REPLACE FUNCTION send_email_notification(
  operation_type text,
  table_name text,
  affected_rows integer DEFAULT NULL,
  operation_data jsonb DEFAULT NULL,
  backup_location text DEFAULT NULL
)
RETURNS void AS $$
DECLARE
  function_url text;
  payload jsonb;
  response text;
BEGIN
  -- Get the Supabase Edge Function URL from environment or use default
  -- You'll need to set this in your Supabase project settings
  function_url := current_setting('app.edge_function_url', true);

  IF function_url IS NULL OR function_url = '' THEN
    -- Default URL pattern - replace with your actual project URL
    function_url := 'https://your-project.supabase.co/functions/v1/database-notifications';
    RAISE WARNING 'Edge function URL not configured. Using default: %', function_url;
  END IF;

  -- Build the payload
  payload := jsonb_build_object(
    'operation_type', operation_type,
    'table_name', table_name,
    'affected_rows', affected_rows,
    'operation_data', operation_data,
    'backup_location', backup_location,
    'timestamp', now()
  );

  -- Note: This is a placeholder for the actual HTTP request
  -- In a real implementation, you would use an extension like http or pg_net
  -- For now, we'll just log the notification
  RAISE NOTICE 'EMAIL NOTIFICATION: % for table % - Payload: %', operation_type, table_name, payload;

  -- Insert into audit log for tracking
  INSERT INTO audit_log (operation_type, table_name, affected_rows, operation_data, performed_by, backup_created, backup_location)
  VALUES ('EMAIL_NOTIFICATION', table_name, affected_rows, payload, 'SYSTEM',
          CASE WHEN backup_location IS NOT NULL THEN TRUE ELSE FALSE END, backup_location);

EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to send email notification: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Enhanced backup function with email notifications
CREATE OR REPLACE FUNCTION create_automatic_backup()
RETURNS void AS $$
DECLARE
  backup_timestamp text;
  table_record record;
  backup_location text;
BEGIN
  backup_timestamp := to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');
  backup_location := 'backup_data schema with timestamp: ' || backup_timestamp;

  -- Create backup tables for all main tables
  FOR table_record IN
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN ('audit_log')
  LOOP
    EXECUTE format('CREATE TABLE IF NOT EXISTS backup_data.%I_%s AS SELECT * FROM public.%I',
                   table_record.table_name, backup_timestamp, table_record.table_name);
  END LOOP;

  -- Log the backup creation
  INSERT INTO audit_log (operation_type, table_name, performed_by, backup_created, backup_location)
  VALUES ('BACKUP', 'ALL_TABLES', 'SYSTEM', TRUE, backup_location);

  -- Send email notification
  PERFORM send_email_notification('BACKUP', 'ALL_TABLES', NULL, NULL, backup_location);

  RAISE NOTICE 'Automatic backup created with timestamp: %', backup_timestamp;
END;
$$ LANGUAGE plpgsql;

-- Enhanced safe wipe function with email notifications
CREATE OR REPLACE FUNCTION safe_wipe_all_data()
RETURNS void AS $$
DECLARE
  total_users integer;
  total_messages integer;
  total_files integer;
  total_projects integer;
  total_access integer;
  backup_location text;
  operation_data jsonb;
BEGIN
  -- Count existing data
  SELECT COUNT(*) INTO total_users FROM users;
  SELECT COUNT(*) INTO total_messages FROM messages;
  SELECT COUNT(*) INTO total_files FROM files;
  SELECT COUNT(*) INTO total_projects FROM projects;
  SELECT COUNT(*) INTO total_access FROM temporary_access;

  -- Create comprehensive backup
  PERFORM create_automatic_backup();
  backup_location := 'backup_data schema with timestamp: ' || to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');

  -- Truncate all tables (preserving structure)
  TRUNCATE TABLE removal_requests CASCADE;
  TRUNCATE TABLE admin_activity_log CASCADE;
  TRUNCATE TABLE temporary_access CASCADE;
  TRUNCATE TABLE files CASCADE;
  TRUNCATE TABLE messages CASCADE;
  TRUNCATE TABLE projects CASCADE;
  TRUNCATE TABLE users CASCADE;
  TRUNCATE TABLE contact_requests CASCADE;
  TRUNCATE TABLE access_requests CASCADE;

  -- Prepare operation data
  operation_data := jsonb_build_object(
    'total_users', total_users,
    'total_messages', total_messages,
    'total_files', total_files,
    'total_projects', total_projects,
    'total_access', total_access,
    'wipe_timestamp', now()
  );

  -- Log the wipe operation
  INSERT INTO audit_log (operation_type, table_name, affected_rows, operation_data, performed_by, backup_created, backup_location)
  VALUES ('WIPE', 'ALL_TABLES',
          total_users + total_messages + total_files + total_projects + total_access,
          operation_data, 'ADMIN', TRUE, backup_location);

  -- Send critical email notification
  PERFORM send_email_notification('WIPE', 'ALL_TABLES',
          total_users + total_messages + total_files + total_projects + total_access,
          operation_data, backup_location);

  RAISE NOTICE 'Database wiped successfully. Backup created: %', backup_location;
  RAISE NOTICE 'Total data wiped - Users: %, Messages: %, Files: %, Projects: %, Access: %',
               total_users, total_messages, total_files, total_projects, total_access;
END;
$$ LANGUAGE plpgsql;

-- Enhanced safe user deletion with email notifications
CREATE OR REPLACE FUNCTION safe_delete_user_data(target_username text)
RETURNS void AS $$
DECLARE
  deleted_messages integer;
  deleted_files integer;
  deleted_projects integer;
  deleted_access integer;
  deleted_user integer;
  backup_location text;
  operation_data jsonb;
BEGIN
  -- Create backup before deletion
  PERFORM create_automatic_backup();
  backup_location := 'backup_data schema with timestamp: ' || to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');

  -- Count and delete messages
  SELECT COUNT(*) INTO deleted_messages FROM messages
  WHERE sender_username = target_username OR recipient_username = target_username;

  DELETE FROM messages
  WHERE sender_username = target_username OR recipient_username = target_username;

  -- Count and delete files
  SELECT COUNT(*) INTO deleted_files FROM files
  WHERE uploader_username = target_username OR target_username = target_username;

  DELETE FROM files
  WHERE uploader_username = target_username OR target_username = target_username;

  -- Count and delete projects
  SELECT COUNT(*) INTO deleted_projects FROM projects WHERE client_username = target_username;
  DELETE FROM projects WHERE client_username = target_username;

  -- Count and delete temporary access
  SELECT COUNT(*) INTO deleted_access FROM temporary_access WHERE client_username = target_username;
  DELETE FROM temporary_access WHERE client_username = target_username;

  -- Count and delete user
  SELECT COUNT(*) INTO deleted_user FROM users WHERE username = target_username;
  DELETE FROM users WHERE username = target_username;

  -- Prepare operation data
  operation_data := jsonb_build_object(
    'username', target_username,
    'deleted_messages', deleted_messages,
    'deleted_files', deleted_files,
    'deleted_projects', deleted_projects,
    'deleted_access', deleted_access,
    'deleted_user', deleted_user
  );

  -- Log the deletion
  INSERT INTO audit_log (operation_type, table_name, affected_rows, operation_data, performed_by, backup_created, backup_location)
  VALUES ('DELETE', 'USER_DATA',
          deleted_messages + deleted_files + deleted_projects + deleted_access + deleted_user,
          operation_data, 'ADMIN', TRUE, backup_location);

  -- Send email notification
  PERFORM send_email_notification('DELETE', 'USER_DATA',
          deleted_messages + deleted_files + deleted_projects + deleted_access + deleted_user,
          operation_data, backup_location);

  RAISE NOTICE 'User data deleted for: % (Messages: %, Files: %, Projects: %, Access: %, User: %)',
               target_username, deleted_messages, deleted_files, deleted_projects, deleted_access, deleted_user;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- INITIALIZATION NOTICE
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'DASWOS PROTECTED DATABASE SCHEMA INITIALIZED';
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'Database protection features enabled:';
  RAISE NOTICE '✅ Automatic backup creation before destructive operations';
  RAISE NOTICE '✅ Audit logging for all database operations';
  RAISE NOTICE '✅ Safe deletion functions with data recovery';
  RAISE NOTICE '✅ Email notification system (requires Edge Function setup)';
  RAISE NOTICE '✅ Data recovery capabilities from backup schema';
  RAISE NOTICE '';
  RAISE NOTICE 'Available safe operations:';
  RAISE NOTICE '• SELECT safe_delete_user_data(''username'');';
  RAISE NOTICE '• SELECT safe_wipe_all_data();';
  RAISE NOTICE '• SELECT create_automatic_backup();';
  RAISE NOTICE '';
  RAISE NOTICE 'See DATABASE_MANAGEMENT_INSTRUCTIONS.md for detailed usage.';
  RAISE NOTICE '=============================================================================';
END $$;
