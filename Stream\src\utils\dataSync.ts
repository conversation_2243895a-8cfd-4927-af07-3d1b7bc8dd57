// Data Synchronization System
// Automatically syncs cached frontend data back to database when database is restored

import { supabase } from '../lib/supabaseClient';

export interface SyncableData {
  id: string;
  table: string;
  data: any;
  timestamp: string;
  synced: boolean;
}

export class DataSyncManager {
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Start automatic sync every 30 seconds
  startAutoSync() {
    if (this.syncInterval) return;
    
    this.syncInterval = setInterval(() => {
      this.performSync();
    }, 30000); // 30 seconds
    
    // Also run immediately
    this.performSync();
  }

  // Stop automatic sync
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Store data locally with sync flag
  async storeForSync(table: string, data: any) {
    const syncData: SyncableData = {
      id: data.id || crypto.randomUUID(),
      table,
      data,
      timestamp: new Date().toISOString(),
      synced: false
    };

    // Store in localStorage for persistence
    const existingData = this.getUnsyncedData();
    existingData.push(syncData);
    localStorage.setItem('daswos_unsynced_data', JSON.stringify(existingData));

    // Try immediate sync
    await this.syncSingleItem(syncData);
    
    return syncData.id;
  }

  // Get all unsynced data from localStorage
  getUnsyncedData(): SyncableData[] {
    try {
      const data = localStorage.getItem('daswos_unsynced_data');
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  // Mark item as synced
  markAsSynced(id: string) {
    const unsyncedData = this.getUnsyncedData();
    const updatedData = unsyncedData.map(item => 
      item.id === id ? { ...item, synced: true } : item
    );
    localStorage.setItem('daswos_unsynced_data', JSON.stringify(updatedData));
  }

  // Remove synced items from localStorage
  cleanupSyncedData() {
    const unsyncedData = this.getUnsyncedData();
    const stillUnsynced = unsyncedData.filter(item => !item.synced);
    localStorage.setItem('daswos_unsynced_data', JSON.stringify(stillUnsynced));
  }

  // Perform full sync operation
  async performSync() {
    if (this.isRunning) return;
    this.isRunning = true;

    try {
      console.log('🔄 Starting data sync...');
      
      const unsyncedData = this.getUnsyncedData();
      const unsynced = unsyncedData.filter(item => !item.synced);
      
      if (unsynced.length === 0) {
        console.log('✅ No data to sync');
        this.isRunning = false;
        return;
      }

      console.log(`📊 Found ${unsynced.length} items to sync`);

      // Group by table for batch operations
      const byTable = unsynced.reduce((acc, item) => {
        if (!acc[item.table]) acc[item.table] = [];
        acc[item.table].push(item);
        return acc;
      }, {} as Record<string, SyncableData[]>);

      // Sync each table
      for (const [table, items] of Object.entries(byTable)) {
        await this.syncTable(table, items);
      }

      // Cleanup synced items
      this.cleanupSyncedData();
      
      console.log('✅ Data sync completed');
    } catch (error) {
      console.error('❌ Data sync failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // Sync a single item immediately
  async syncSingleItem(item: SyncableData): Promise<boolean> {
    try {
      // Check if item already exists in database
      const { data: existing } = await supabase
        .from(item.table)
        .select('id')
        .eq('id', item.data.id)
        .single();

      if (existing) {
        // Item already exists, mark as synced
        this.markAsSynced(item.id);
        return true;
      }

      // Insert new item
      const { error } = await supabase
        .from(item.table)
        .insert([item.data]);

      if (error) {
        console.error(`Failed to sync ${item.table}:`, error);
        return false;
      }

      // Mark as synced
      this.markAsSynced(item.id);
      console.log(`✅ Synced ${item.table} item:`, item.data.id);
      return true;

    } catch (error) {
      console.error(`Error syncing ${item.table}:`, error);
      return false;
    }
  }

  // Sync all items for a specific table
  async syncTable(table: string, items: SyncableData[]) {
    console.log(`🔄 Syncing ${items.length} items to ${table} table`);

    for (const item of items) {
      await this.syncSingleItem(item);
      // Small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // Recovery function: scan localStorage and restore all cached data
  async recoverAllData() {
    console.log('🚨 Starting data recovery from localStorage...');

    const tables = ['access_requests', 'users', 'messages', 'files', 'contact_requests'];
    let totalRecovered = 0;

    for (const table of tables) {
      const cachedData = this.getCachedDataForTable(table);
      if (cachedData.length > 0) {
        console.log(`📦 Found ${cachedData.length} cached ${table} items`);
        
        for (const data of cachedData) {
          await this.storeForSync(table, data);
          totalRecovered++;
        }
      }
    }

    console.log(`🎯 Recovery complete: ${totalRecovered} items queued for sync`);
    
    // Trigger immediate sync
    await this.performSync();
  }

  // Get cached data for a specific table from various localStorage keys
  getCachedDataForTable(table: string): any[] {
    const cacheKeys = {
      access_requests: ['daswos_access_requests', 'cached_access_requests'],
      users: ['daswos_users', 'cached_users'],
      messages: ['daswos_messages', 'cached_messages'],
      files: ['daswos_files', 'cached_files'],
      contact_requests: ['daswos_contact_requests', 'cached_contact_requests']
    };

    const keys = cacheKeys[table as keyof typeof cacheKeys] || [];
    const allData: any[] = [];

    for (const key of keys) {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const parsed = JSON.parse(data);
          if (Array.isArray(parsed)) {
            allData.push(...parsed);
          } else if (parsed) {
            allData.push(parsed);
          }
        }
      } catch (error) {
        console.warn(`Failed to parse cached data for ${key}:`, error);
      }
    }

    return allData;
  }
}

// Global instance
export const dataSyncManager = new DataSyncManager();

// Auto-start sync when module loads
if (typeof window !== 'undefined') {
  dataSyncManager.startAutoSync();
}
