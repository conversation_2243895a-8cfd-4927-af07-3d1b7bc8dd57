-- Add contact_requests table for contact form submissions
-- This table will store general contact/inquiry messages from the contact form
-- Different from access_requests which are specifically for portal access

CREATE TABLE IF NOT EXISTS contact_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new',
  submitted_at timestamptz NOT NULL DEFAULT now(),
  responded_at timestamptz NULL,
  responded_by text NULL,
  notes text NULL
);

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_contact_requests_status ON contact_requests(status);
CREATE INDEX IF NOT EXISTS idx_contact_requests_submitted_at ON contact_requests(submitted_at DESC);
