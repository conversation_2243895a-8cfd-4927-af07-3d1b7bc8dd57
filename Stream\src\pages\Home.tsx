import React from "react";

const Home = () => (
  <section>
    <div className="text-center py-10">
      <h1 className="text-3xl font-extrabold text-white mb-4 drop-shadow-lg opacity-90">Operational Intelligence</h1>
      <h2 className="text-base text-gray-300 mb-4 font-medium tracking-wide">Access by invitation only. Trusted by select enterprises. Powered by human insight and confidential technology.</h2>
      <p className="max-w-xl mx-auto text-gray-300 mb-6 text-base opacity-80">We transform complex operations into efficient, high-performing systems. Our methods are discreet, our results are repeatable, and our proprietary DASWOS platform is strictly internal. We deliver actionable strategies that cut costs and boost productivity—without public fanfare.</p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="flex items-center space-x-2 text-sm">
          <span className="bg-white/10 text-white rounded-full px-2 py-0.5 font-semibold shadow">✓</span>
          <span className="text-gray-300">Identify inefficiencies through confidential interviews and process mapping.</span>
        </div>
        <div className="flex items-center space-x-3">
          <span className="bg-white/10 text-white rounded-full px-3 py-1 font-semibold shadow">✓</span>
          <span className="text-gray-300">Deliver tailored recommendations for process re-engineering and resource optimization.</span>
        </div>
        <div className="flex items-center space-x-3">
          <span className="bg-white/10 text-white rounded-full px-3 py-1 font-semibold shadow">✓</span>
          <span className="text-gray-300">Build custom portals and scripts to streamline daily operations—secure and private.</span>
        </div>
        <div className="flex items-center space-x-3">
          <span className="bg-white/10 text-white rounded-full px-3 py-1 font-semibold shadow">✓</span>
          <span className="text-gray-300">Ongoing support for continuous improvement, always under NDA.</span>
        </div>
      </div>
      <a href="/contact" className="inline-block px-6 py-2 bg-white/10 text-white rounded-2xl font-bold shadow-xl border-2 border-gray-700/40 hover:scale-105 transition-all text-base backdrop-blur">Contact</a>
    </div>
    <div className="mt-8 text-center text-gray-600 text-xs opacity-60">
      <p>Video and testimonials available to approved clients only.</p>
    </div>
  </section>
);

export default Home;
