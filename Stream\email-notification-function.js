// Supabase Edge Function for Database Backup Email Notifications
// Deploy this as a Supabase Edge Function to handle email notifications

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { operation_type, table_name, backup_location, affected_rows, operation_data } = await req.json()

    // Email configuration (use environment variables)
    const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
    const FROM_EMAIL = Deno.env.get('FROM_EMAIL') || '<EMAIL>'
    const TO_EMAIL = Deno.env.get('TO_EMAIL') || '<EMAIL>'

    if (!SENDGRID_API_KEY) {
      throw new Error('SENDGRID_API_KEY environment variable is required')
    }

    // Create email content based on operation type
    let subject = ''
    let htmlContent = ''

    switch (operation_type) {
      case 'WIPE':
        subject = '🚨 CRITICAL: Database Wipe Operation Performed - DASWOS'
        htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #dc2626; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h1 style="margin: 0; font-size: 24px;">🚨 CRITICAL DATABASE OPERATION</h1>
            </div>
            
            <h2>Database Wipe Operation Performed</h2>
            <p><strong>Operation:</strong> Complete database wipe</p>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Affected Rows:</strong> ${affected_rows || 'Unknown'}</p>
            
            ${operation_data ? `
              <h3>Operation Details:</h3>
              <ul>
                <li><strong>Total Users:</strong> ${operation_data.total_users || 0}</li>
                <li><strong>Total Messages:</strong> ${operation_data.total_messages || 0}</li>
                <li><strong>Total Files:</strong> ${operation_data.total_files || 0}</li>
              </ul>
            ` : ''}
            
            <div style="background-color: #059669; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin: 0;">✅ Backup Created</h3>
              <p style="margin: 5px 0 0 0;"><strong>Location:</strong> ${backup_location}</p>
            </div>
            
            <h3>Next Steps:</h3>
            <ol>
              <li>Verify the backup was created successfully</li>
              <li>Check if this operation was intentional</li>
              <li>If accidental, restore from backup immediately</li>
              <li>Review database access logs</li>
            </ol>
            
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin-top: 20px;">
              <p><strong>Recovery Instructions:</strong> See DATABASE_MANAGEMENT_INSTRUCTIONS.md for detailed recovery procedures.</p>
            </div>
          </div>
        `
        break

      case 'DELETE':
        subject = '⚠️ Database Deletion Operation - DASWOS'
        htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #d97706; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h1 style="margin: 0; font-size: 24px;">⚠️ DATABASE DELETION OPERATION</h1>
            </div>
            
            <h2>Data Deletion Performed</h2>
            <p><strong>Operation:</strong> ${table_name} deletion</p>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Affected Rows:</strong> ${affected_rows || 'Unknown'}</p>
            
            ${operation_data ? `
              <h3>Deletion Details:</h3>
              <pre style="background-color: #f3f4f6; padding: 10px; border-radius: 5px; overflow-x: auto;">
${JSON.stringify(operation_data, null, 2)}
              </pre>
            ` : ''}
            
            <div style="background-color: #059669; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin: 0;">✅ Backup Created</h3>
              <p style="margin: 5px 0 0 0;"><strong>Location:</strong> ${backup_location}</p>
            </div>
            
            <p>This operation was logged and can be reversed if necessary using the backup data.</p>
          </div>
        `
        break

      case 'BACKUP':
        subject = '📦 Database Backup Created - DASWOS'
        htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #059669; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h1 style="margin: 0; font-size: 24px;">📦 DATABASE BACKUP CREATED</h1>
            </div>
            
            <h2>Backup Operation Completed</h2>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Backup Location:</strong> ${backup_location}</p>
            
            <p>A new database backup has been created successfully. This backup can be used to restore data if needed.</p>
            
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin-top: 20px;">
              <p><strong>Note:</strong> Regular backups help ensure data safety and recovery capabilities.</p>
            </div>
          </div>
        `
        break

      default:
        subject = `Database Operation: ${operation_type} - DASWOS`
        htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Database Operation Notification</h2>
            <p><strong>Operation:</strong> ${operation_type}</p>
            <p><strong>Table:</strong> ${table_name}</p>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Affected Rows:</strong> ${affected_rows || 'Unknown'}</p>
            
            ${backup_location ? `<p><strong>Backup Location:</strong> ${backup_location}</p>` : ''}
          </div>
        `
    }

    // Send email using SendGrid
    const emailResponse = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [
          {
            to: [{ email: TO_EMAIL }],
            subject: subject
          }
        ],
        from: { email: FROM_EMAIL },
        content: [
          {
            type: 'text/html',
            value: htmlContent
          }
        ]
      })
    })

    if (!emailResponse.ok) {
      const errorText = await emailResponse.text()
      throw new Error(`SendGrid API error: ${emailResponse.status} - ${errorText}`)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Email notification sent successfully',
        operation: operation_type,
        timestamp: new Date().toISOString()
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error sending email notification:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

/* 
DEPLOYMENT INSTRUCTIONS:

1. Create a new Supabase Edge Function:
   supabase functions new database-notifications

2. Replace the generated index.ts with this code

3. Set environment variables in Supabase:
   - SENDGRID_API_KEY: Your SendGrid API key
   - FROM_EMAIL: Email address to send from (optional, <NAME_EMAIL>)
   - TO_EMAIL: Email address to send to (optional, <NAME_EMAIL>)

4. Deploy the function:
   supabase functions deploy database-notifications

5. Get the function URL and update the database trigger to call it

6. Test the function:
   curl -X POST 'https://your-project.supabase.co/functions/v1/database-notifications' \
   -H 'Authorization: Bearer YOUR_ANON_KEY' \
   -H 'Content-Type: application/json' \
   -d '{"operation_type":"BACKUP","table_name":"test","backup_location":"test_backup"}'
*/
