-- Add new columns to temporary_access table for real-time admin access tracking
-- Run this script to update your existing database

-- Add admin_currently_accessing column
ALTER TABLE temporary_access 
ADD COLUMN IF NOT EXISTS admin_currently_accessing boolean NOT NULL DEFAULT FALSE;

-- Add admin_session_started_at column  
ALTER TABLE temporary_access 
ADD COLUMN IF NOT EXISTS admin_session_started_at timestamptz NULL;

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'temporary_access' 
AND column_name IN ('admin_currently_accessing', 'admin_session_started_at')
ORDER BY column_name;

-- Show current temporary_access table structure
\d temporary_access;
