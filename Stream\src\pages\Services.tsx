import React from "react";

const Services = () => (
  <section>
    <h1 className="text-xl font-bold text-white mb-2">Our Services: Comprehensive Workflow Optimization</h1>
    <div className="mb-4 text-xs">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div className="bg-gray-800 rounded-lg p-2">
          <h2 className="text-base font-semibold text-blue-400 mb-1">In-Depth Operational Assessment</h2>
          <p className="text-gray-300 text-xs">Manual interviews, process mapping, and data review to uncover pain points and baselines.</p>
        </div>
        <div className="bg-gray-800 rounded-lg p-2">
          <h2 className="text-base font-semibold text-blue-400 mb-1">Strategic Recommendations</h2>
          <p className="text-gray-300 text-xs">Actionable plans for re-engineering, automation, and resource allocation.</p>
        </div>
        <div className="bg-gray-800 rounded-lg p-2">
          <h2 className="text-base font-semibold text-blue-400 mb-1">Custom Development</h2>
          <p className="text-gray-300 text-xs">Bespoke websites, portals, dashboards, and scripts to support optimized workflows—repeatable across industries.</p>
        </div>
        <div className="bg-gray-800 rounded-lg p-2">
          <h2 className="text-base font-semibold text-blue-400 mb-1">Ongoing Monitoring</h2>
          <p className="text-gray-300 text-xs">Retainer services for continuous refinement using proven frameworks (with DASWOS integration coming soon).</p>
        </div>
      </div>
    </div>
    <div className="mb-4 text-xs">
      <h2 className="text-base font-semibold text-gray-200 mb-1">How It Works</h2>
      <ol className="list-decimal pl-4 text-gray-200">
        <li>Discovery call to understand your challenges.</li>
        <li>On-site or virtual assessment.</li>
        <li>Detailed analysis and recommendations.</li>
        <li>Implementation guidance and follow-up.</li>
      </ol>
    </div>
    <div className="mb-2 text-gray-300 text-xs">
      <strong>Pricing Teaser:</strong> Engagements start at $50K for assessments. Contact us for a customized quote.
    </div>
    <a href="/contact" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition text-xs">Let's discuss how we can optimize your operations—get in touch!</a>
  </section>
);

export default Services;
