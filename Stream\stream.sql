-- Supabase SQL schema for Stream client/company portal
-- COMPLETE SCHEMA WITH ALL FEATURES
-- Version: 2024-01-29 - Full Bidirectional Conversation System

-- Table: access_requests (Portal access management)
CREATE TABLE IF NOT EXISTS access_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text NOT NULL,
  status text NOT NULL DEFAULT 'unverified',
  requested_at timestamptz NOT NULL DEFAULT now(),
  approved_at timestamptz NULL,
  approved_by text NULL,
  generated_username text NULL,
  generated_password text NULL,
  rejection_reason text NULL,
  email_verification_token text NULL,
  email_verified_at timestamptz NULL,
  verification_expires_at timestamptz NULL
);

-- Table: users (Approved client portal users with username-based login)
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  username text NOT NULL UNIQUE,
  email text NOT NULL,
  password_hash text NOT NULL,
  name text,
  company text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  last_login timestamptz NULL
);

-- Table: contact_requests (Contact form submissions)
CREATE TABLE IF NOT EXISTS contact_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new',
  submitted_at timestamptz NOT NULL DEFAULT now(),
  responded_at timestamptz NULL,
  responded_by text NULL,
  notes text NULL
);

-- Table: messages (Bidirectional client-admin messaging system)
CREATE TABLE IF NOT EXISTS messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_username text NOT NULL, -- Username of message sender (client username or 'admin')
  recipient_username text NOT NULL, -- Username of message recipient (client username or 'admin')
  content text NOT NULL, -- Message content
  read_by_admin boolean NOT NULL DEFAULT FALSE, -- Has admin seen this message
  read_by_client boolean NOT NULL DEFAULT FALSE, -- Has client seen this message
  deleted_by_sender boolean NOT NULL DEFAULT FALSE, -- Has sender deleted this message from their view
  deleted_by_recipient boolean NOT NULL DEFAULT FALSE, -- Has recipient deleted this message from their view
  created_at timestamptz NOT NULL DEFAULT now() -- Message timestamp
);

-- Table: files (Bidirectional file sharing and management system)
CREATE TABLE IF NOT EXISTS files (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  uploader_username text NOT NULL, -- Username who uploaded the file (client username or 'admin')
  filename text NOT NULL, -- Original filename
  url text NOT NULL, -- File storage URL (placeholder for now)
  file_size integer, -- File size in bytes
  file_type text, -- MIME type of the file
  admin_comment text, -- Admin feedback/comment on client files
  reviewed_by_admin boolean NOT NULL DEFAULT FALSE, -- Has admin reviewed this file
  uploaded_by_admin boolean NOT NULL DEFAULT FALSE, -- Was this uploaded by admin
  target_username text, -- For admin uploads: which client should see this file
  read_by_client boolean NOT NULL DEFAULT FALSE, -- Has target client seen this file
  deleted_by_uploader boolean NOT NULL DEFAULT FALSE, -- Has uploader deleted this file from their view
  deleted_by_target boolean NOT NULL DEFAULT FALSE, -- Has target user deleted this file from their view
  created_at timestamptz NOT NULL DEFAULT now() -- Upload timestamp
);

-- Table: temporary_access (Client-granted temporary admin access)
CREATE TABLE IF NOT EXISTS temporary_access (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  client_username text NOT NULL, -- Client granting access
  granted_by_client_at timestamptz NOT NULL DEFAULT now(), -- When client granted access
  expires_at timestamptz NOT NULL, -- When access expires
  access_duration_days integer NOT NULL, -- Duration in days for reference
  status text NOT NULL DEFAULT 'active', -- 'active', 'expired', 'revoked'
  revoked_at timestamptz NULL, -- When access was revoked (if applicable)
  revoked_by text NULL, -- Who revoked access: 'client' or 'admin'
  last_used_at timestamptz NULL, -- When admin last used this access
  access_token text NOT NULL UNIQUE, -- Unique token for this access grant
  admin_currently_accessing boolean NOT NULL DEFAULT FALSE, -- Is admin currently using this access
  admin_session_started_at timestamptz NULL, -- When admin started current session
  admin_session_ended_at timestamptz NULL, -- When admin ended current session
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Table: admin_activity_log (Track what admins do during temporary access sessions)
CREATE TABLE IF NOT EXISTS admin_activity_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temporary_access_id uuid NOT NULL REFERENCES temporary_access(id) ON DELETE CASCADE,
  client_username text NOT NULL, -- Which client's account was affected
  admin_username text, -- Which admin performed the action (if available)
  action_type text NOT NULL, -- 'message_sent', 'message_deleted', 'file_uploaded', 'file_deleted', 'user_updated', 'password_changed', etc.
  action_description text NOT NULL, -- Human-readable description of what was done
  target_table text, -- Which database table was affected (messages, files, users, etc.)
  target_id text, -- ID of the specific record that was affected
  old_values jsonb, -- Previous values before the change (for updates/deletes)
  new_values jsonb, -- New values after the change (for creates/updates)
  ip_address text, -- IP address of the admin (for security)
  user_agent text, -- Browser/device info
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Table: removal_requests (Requests to remove shared messages/files)
CREATE TABLE IF NOT EXISTS removal_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  requester_username text NOT NULL, -- Who is requesting the removal
  target_username text NOT NULL, -- Who needs to approve the removal
  item_type text NOT NULL, -- 'message' or 'file'
  item_id uuid NOT NULL, -- ID of the message or file to be removed
  reason text, -- Optional reason for removal request
  status text NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'denied'
  responded_at timestamptz, -- When the target user responded
  request_count integer NOT NULL DEFAULT 1, -- Track how many times this item has been requested for deletion
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Index for efficient querying of admin activities by session
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_session ON admin_activity_log(temporary_access_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_client ON admin_activity_log(client_username);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at);

-- Performance indexes for optimal query speed
-- Access requests indexes
CREATE INDEX IF NOT EXISTS idx_access_requests_status ON access_requests(status);
CREATE INDEX IF NOT EXISTS idx_access_requests_email ON access_requests(email);
CREATE INDEX IF NOT EXISTS idx_access_requests_requested_at ON access_requests(requested_at DESC);

-- Users indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Contact requests indexes
CREATE INDEX IF NOT EXISTS idx_contact_requests_status ON contact_requests(status);
CREATE INDEX IF NOT EXISTS idx_contact_requests_submitted_at ON contact_requests(submitted_at DESC);

-- Messages indexes (for conversation queries)
CREATE INDEX IF NOT EXISTS idx_messages_sender_username ON messages(sender_username);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_username ON messages(recipient_username);
CREATE INDEX IF NOT EXISTS idx_messages_read_by_admin ON messages(read_by_admin);
CREATE INDEX IF NOT EXISTS idx_messages_read_by_client ON messages(read_by_client);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(sender_username, recipient_username);

-- Files indexes (for file management queries)
CREATE INDEX IF NOT EXISTS idx_files_uploader_username ON files(uploader_username);
CREATE INDEX IF NOT EXISTS idx_files_target_username ON files(target_username);
CREATE INDEX IF NOT EXISTS idx_files_reviewed_by_admin ON files(reviewed_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by_admin ON files(uploaded_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_read_by_client ON files(read_by_client);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_admin_uploads ON files(uploaded_by_admin, target_username);

-- Indexes for admin_activity_log
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_temporary_access_id ON admin_activity_log(temporary_access_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_client_username ON admin_activity_log(client_username);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at DESC);

-- Indexes for removal_requests
CREATE INDEX IF NOT EXISTS idx_removal_requests_requester_username ON removal_requests(requester_username);
CREATE INDEX IF NOT EXISTS idx_removal_requests_target_username ON removal_requests(target_username);
CREATE INDEX IF NOT EXISTS idx_removal_requests_status ON removal_requests(status);
CREATE INDEX IF NOT EXISTS idx_removal_requests_item_type_id ON removal_requests(item_type, item_id);

-- Indexes for new deletion columns
CREATE INDEX IF NOT EXISTS idx_messages_deleted_by_sender ON messages(deleted_by_sender);
CREATE INDEX IF NOT EXISTS idx_messages_deleted_by_recipient ON messages(deleted_by_recipient);
CREATE INDEX IF NOT EXISTS idx_files_deleted_by_uploader ON files(deleted_by_uploader);
CREATE INDEX IF NOT EXISTS idx_files_deleted_by_target ON files(deleted_by_target);

-- Add request_count column to existing removal_requests table if it doesn't exist
ALTER TABLE removal_requests ADD COLUMN IF NOT EXISTS request_count integer NOT NULL DEFAULT 1;

-- Projects system for client portal
-- This adds project-based organization to messages and files

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  client_username text NOT NULL REFERENCES users(username) ON DELETE CASCADE,
  created_by_admin boolean NOT NULL DEFAULT TRUE,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Add project_id to messages table
ALTER TABLE messages ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;

-- Add project_id to files table
ALTER TABLE files ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;

-- Add client_comment field to files table for client commenting
ALTER TABLE files ADD COLUMN IF NOT EXISTS client_comment text;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_client_username ON projects(client_username);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);
CREATE INDEX IF NOT EXISTS idx_files_project_id ON files(project_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default projects for existing clients
-- This will help with testing the new project system
INSERT INTO projects (name, description, client_username, created_by_admin, status)
SELECT
    'General Communication' as name,
    'Default project for general messages and files' as description,
    username as client_username,
    TRUE as created_by_admin,
    'active' as status
FROM users
WHERE username != 'admin'  -- Exclude admin user, only include actual clients
ON CONFLICT DO NOTHING;

-- Update existing messages to use the default project
UPDATE messages
SET project_id = (
    SELECT p.id
    FROM projects p
    WHERE p.client_username = CASE
        WHEN messages.sender_username = 'admin' THEN messages.recipient_username
        ELSE messages.sender_username
    END
    AND p.name = 'General Communication'
    LIMIT 1
)
WHERE project_id IS NULL;

-- Update existing files to use the default project
UPDATE files
SET project_id = (
    SELECT p.id
    FROM projects p
    WHERE p.client_username = CASE
        WHEN files.uploader_username = 'admin' THEN files.target_username
        ELSE files.uploader_username
    END
    AND p.name = 'General Communication'
    LIMIT 1
)
WHERE project_id IS NULL;
