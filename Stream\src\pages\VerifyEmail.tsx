import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';

const VerifyEmail = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyEmail = async () => {
      // Get token from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');

      if (!token) {
        setStatus('error');
        setMessage('Invalid verification link. No token provided.');
        return;
      }

      try {
        // Find the access request with this token
        const { data: requests, error } = await supabase
          .from('access_requests')
          .select('*')
          .eq('email_verification_token', token)
          .limit(1);

        if (error) {
          setStatus('error');
          setMessage('Error verifying email: ' + error.message);
          return;
        }

        if (!requests || requests.length === 0) {
          setStatus('error');
          setMessage('Invalid verification link. Token not found.');
          return;
        }

        const request = requests[0];

        // Check if already verified
        if (request.email_verified_at) {
          setStatus('success');
          setMessage('Email already verified! Your request is pending admin approval.');
          return;
        }

        // Check if token has expired
        const expiresAt = new Date(request.verification_expires_at);
        const now = new Date();
        
        if (now > expiresAt) {
          setStatus('expired');
          setMessage('Verification link has expired. Please submit a new access request.');
          return;
        }

        // Update the request to mark email as verified
        const { error: updateError } = await supabase
          .from('access_requests')
          .update({
            email_verified_at: new Date().toISOString(),
            status: 'pending' // Change from 'unverified' to 'pending'
          })
          .eq('id', request.id);

        if (updateError) {
          setStatus('error');
          setMessage('Error updating verification status: ' + updateError.message);
          return;
        }

        setStatus('success');
        setMessage('Email verified successfully! Your access request is now pending admin approval. You will receive an email once your request is reviewed.');

      } catch (err) {
        setStatus('error');
        setMessage('An unexpected error occurred during verification.');
        console.error('Email verification error:', err);
      }
    };

    verifyEmail();
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md mx-auto p-6 bg-gray-800 rounded-lg shadow-lg text-center">
        <h1 className="text-white text-2xl font-bold mb-6">Email Verification</h1>
        
        {status === 'loading' && (
          <div className="text-gray-300">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            Verifying your email...
          </div>
        )}

        {status === 'success' && (
          <div className="bg-green-900 text-green-300 rounded p-4 mb-4">
            <div className="text-4xl mb-2">✓</div>
            <div className="font-semibold mb-2">Success!</div>
            <div className="text-sm">{message}</div>
          </div>
        )}

        {status === 'error' && (
          <div className="bg-red-900 text-red-300 rounded p-4 mb-4">
            <div className="text-4xl mb-2">✗</div>
            <div className="font-semibold mb-2">Verification Failed</div>
            <div className="text-sm">{message}</div>
          </div>
        )}

        {status === 'expired' && (
          <div className="bg-yellow-900 text-yellow-300 rounded p-4 mb-4">
            <div className="text-4xl mb-2">⏰</div>
            <div className="font-semibold mb-2">Link Expired</div>
            <div className="text-sm">{message}</div>
          </div>
        )}

        <div className="mt-6">
          <a 
            href="/client-portal" 
            className="inline-block px-6 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition"
          >
            Go to Client Portal
          </a>
        </div>

        {(status === 'expired' || status === 'error') && (
          <div className="mt-4">
            <a 
              href="/client-portal" 
              className="text-blue-400 hover:text-blue-300 text-sm underline"
            >
              Submit a new access request
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default VerifyEmail;
