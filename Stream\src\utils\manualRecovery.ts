// Manual Data Recovery Utilities
// Use these functions to manually recover data when database is restored

import { dataSyncManager } from './dataSync';
import { supabase } from '../lib/supabaseClient';

// Manual recovery function that can be called from browser console
export const recoverAllDataNow = async () => {
  console.log('🚨 MANUAL DATA RECOVERY STARTED');
  console.log('This will scan localStorage and restore all cached data to the database');
  
  try {
    await dataSyncManager.recoverAllData();
    console.log('✅ Manual recovery completed successfully');
  } catch (error) {
    console.error('❌ Manual recovery failed:', error);
  }
};

// Check what data is available for recovery
export const checkRecoverableData = () => {
  console.log('📊 CHECKING RECOVERABLE DATA');
  
  const tables = ['access_requests', 'users', 'messages', 'files', 'contact_requests'];
  const summary: Record<string, number> = {};
  
  for (const table of tables) {
    const data = dataSyncManager.getCachedDataForTable(table);
    summary[table] = data.length;
    
    if (data.length > 0) {
      console.log(`📦 ${table}: ${data.length} items found`);
      console.log('Sample data:', data[0]);
    }
  }
  
  const unsynced = dataSyncManager.getUnsyncedData();
  console.log(`🔄 Unsynced items: ${unsynced.length}`);
  
  return summary;
};

// Force sync all unsynced data
export const forceSyncNow = async () => {
  console.log('🔄 FORCING SYNC NOW');
  
  try {
    await dataSyncManager.performSync();
    console.log('✅ Force sync completed');
  } catch (error) {
    console.error('❌ Force sync failed:', error);
  }
};

// Clear all cached data (use with caution!)
export const clearAllCachedData = () => {
  console.log('🗑️ CLEARING ALL CACHED DATA');
  console.warn('⚠️ This will permanently delete all unsynced data!');
  
  const confirm = window.confirm('Are you sure you want to clear all cached data? This cannot be undone!');
  if (!confirm) {
    console.log('❌ Cancelled by user');
    return;
  }
  
  localStorage.removeItem('daswos_unsynced_data');
  localStorage.removeItem('daswos_access_requests');
  localStorage.removeItem('daswos_users');
  localStorage.removeItem('daswos_messages');
  localStorage.removeItem('daswos_files');
  localStorage.removeItem('daswos_contact_requests');
  
  console.log('✅ All cached data cleared');
};

// Test database connectivity
export const testDatabaseConnection = async () => {
  console.log('🔌 TESTING DATABASE CONNECTION');
  
  try {
    const { data, error } = await supabase.from('access_requests').select('id').limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    } else {
      console.log('✅ Database connection successful');
      return true;
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
    return false;
  }
};

// Export all functions to window for browser console access
if (typeof window !== 'undefined') {
  (window as any).dataRecovery = {
    recoverAllDataNow,
    checkRecoverableData,
    forceSyncNow,
    clearAllCachedData,
    testDatabaseConnection
  };
  
  console.log('🛠️ Data recovery tools loaded. Use window.dataRecovery in console:');
  console.log('- window.dataRecovery.checkRecoverableData() - Check what data can be recovered');
  console.log('- window.dataRecovery.recoverAllDataNow() - Recover all cached data');
  console.log('- window.dataRecovery.forceSyncNow() - Force sync unsynced data');
  console.log('- window.dataRecovery.testDatabaseConnection() - Test database connection');
  console.log('- window.dataRecovery.clearAllCachedData() - Clear all cached data (DANGER!)');
}

// Automatic recovery on page load if database is available
export const autoRecoveryOnLoad = async () => {
  // Wait a bit for the app to initialize
  setTimeout(async () => {
    const isConnected = await testDatabaseConnection();
    if (isConnected) {
      const unsynced = dataSyncManager.getUnsyncedData();
      if (unsynced.length > 0) {
        console.log(`🔄 Auto-recovery: Found ${unsynced.length} unsynced items, starting sync...`);
        await dataSyncManager.performSync();
      }
    }
  }, 3000); // 3 second delay
};

// Start auto-recovery
if (typeof window !== 'undefined') {
  autoRecoveryOnLoad();
}
