# Security Features Testing Guide

## Prerequisites
1. Run `security_features_update.sql` in your Supabase SQL Editor
2. Install dependencies: `npm install bcryptjs @types/bcryptjs`
3. Ensure development server is running: `npm run dev`

## 🔐 Password Hashing Testing

### Test 1: New User Creation with Hashed Passwords
1. Go to admin portal and approve a new access request
2. Check the `users` table in Supabase
3. Verify `password_hash` field starts with `$2a$` (bcrypt hash)
4. Confirm the generated password works for login

### Test 2: Legacy Password Support
1. Login with existing demo user: `demo_user` / `SODA`
2. Should work even if password is still plain text
3. System supports both hashed and plain text during migration

## 📧 Email Verification Testing

### Test 1: Access Request with Email Verification
1. Go to `/client-portal`
2. Click "Request Access"
3. Fill out form and submit
4. Check `access_requests` table - status should be `unverified`
5. Verify email verification token is generated
6. Check that request does NOT appear in admin portal yet

### Test 2: Email Verification Process
1. Copy the verification URL from the email (or construct manually):
   `http://localhost:5173/verify-email?token=[TOKEN]`
2. Visit the verification URL
3. Should see success message
4. Check `access_requests` table - status should change to `pending`
5. Verify `email_verified_at` timestamp is set
6. Request should now appear in admin portal

### Test 3: Expired Verification Link
1. In database, set `verification_expires_at` to a past date
2. Try to verify with the token
3. Should see "Link Expired" message

### Test 4: Invalid Token
1. Visit verification URL with random token
2. Should see "Invalid verification link" error

## 🔄 Password Reset Testing

### Test 1: Password Reset Flow
1. Go to `/client-portal`
2. Click "Forgot Password?"
3. Enter email of existing user
4. Check email for temporary password
5. Login with username and temporary password
6. Verify login works

### Test 2: Invalid Email Reset
1. Try password reset with non-existent email
2. Should see "No account found" error

### Test 3: Inactive User Reset
1. Set a user's `is_active` to false in database
2. Try password reset with their email
3. Should not find the account

## ⏱️ Rate Limiting Testing

### Test 1: Login Rate Limiting
1. Try to login with wrong password 6 times quickly
2. Should see rate limit message after 5 attempts
3. Wait 15 minutes or check `RateLimiter` class for reset

### Test 2: Access Request Rate Limiting
1. Submit 4 access requests with same email quickly
2. Should see rate limit message after 3 attempts
3. Wait 1 hour for reset

### Test 3: Duplicate Request Prevention
1. Submit an access request
2. Try to submit another with same email
3. Should see "already have a pending request" message

## 🔍 Database Authentication Testing

### Test 1: Username-Based Login
1. Use credentials: `henryele_henr_olr` / `K@WpPEe8vwSz`
2. Should successfully authenticate against database
3. Verify `last_login` timestamp updates

### Test 2: Case Sensitivity
1. Try login with wrong case username
2. Should fail (usernames are case-sensitive)

### Test 3: Inactive User Login
1. Set user's `is_active` to false
2. Try to login
3. Should fail authentication

## 🛡️ Security Validation

### Test 1: SQL Injection Prevention
1. Try login with username: `admin'; DROP TABLE users; --`
2. Should safely handle and reject

### Test 2: XSS Prevention
1. Submit access request with name: `<script>alert('xss')</script>`
2. Should display safely without executing script

### Test 3: Token Security
1. Verification tokens should be UUIDs (36 characters)
2. Should be cryptographically random
3. Should expire after 24 hours

## 📊 Admin Portal Security Testing

### Test 1: Verified Requests Only
1. Submit unverified access request
2. Check admin portal - should NOT appear
3. Verify the request - should then appear

### Test 2: Approval with Hashed Passwords
1. Approve a verified request
2. Check generated password is complex (12+ chars, mixed case, symbols)
3. Verify password is hashed in database
4. Test login with generated credentials

### Test 3: Rejection Tracking
1. Reject an access request
2. Verify `rejection_reason` is stored
3. Check status updates correctly

## 🔧 Error Handling Testing

### Test 1: Database Connection Errors
1. Temporarily break Supabase connection
2. Try various operations
3. Should show user-friendly error messages

### Test 2: Malformed Requests
1. Submit forms with missing required fields
2. Should show validation errors

### Test 3: Network Timeouts
1. Simulate slow network
2. Verify loading states work correctly

## 📈 Performance Testing

### Test 1: Large Dataset Handling
1. Create 100+ access requests
2. Check admin portal loads quickly
3. Verify pagination or limits work

### Test 2: Concurrent Users
1. Open multiple browser tabs
2. Try simultaneous operations
3. Verify no race conditions

## 🔍 Monitoring & Logging

### Test 1: Security Audit Log
1. Check if `security_audit_log` table exists
2. Verify login events are logged
3. Check failed attempts are recorded

### Test 2: Rate Limit Tracking
1. Verify `login_attempts` table records attempts
2. Check timestamps and identifiers

## ✅ Expected Results Summary

| Feature | Status | Test Result |
|---------|--------|-------------|
| Password Hashing | ✅ | Bcrypt hashes generated |
| Email Verification | ✅ | 24-hour token system |
| Password Reset | ✅ | Temporary password via email |
| Rate Limiting | ✅ | 5 login, 3 request limits |
| Database Auth | ✅ | Username-based login |
| Admin Security | ✅ | Verified requests only |

## 🚨 Security Checklist

- [ ] All passwords are hashed with bcrypt
- [ ] Email verification required for requests
- [ ] Rate limiting prevents brute force
- [ ] SQL injection protection active
- [ ] XSS prevention in place
- [ ] Tokens expire appropriately
- [ ] Audit logging functional
- [ ] Error messages don't leak info
- [ ] HTTPS enforced (production)
- [ ] Environment variables secured

## 🔧 Troubleshooting

**Login fails with correct credentials:**
- Check if password is hashed vs plain text
- Verify `is_active` is true
- Check rate limiting status

**Email verification not working:**
- Verify token in database matches URL
- Check expiration timestamp
- Ensure status transitions correctly

**Rate limiting too aggressive:**
- Adjust limits in `RateLimiter` constructor
- Clear `login_attempts` table for testing

**Admin portal empty:**
- Verify requests are email-verified
- Check database query filters
- Confirm Supabase permissions
