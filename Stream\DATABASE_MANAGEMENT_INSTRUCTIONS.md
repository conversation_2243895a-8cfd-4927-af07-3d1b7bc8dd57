# DASWOS Database Management Instructions

## ⚠️ CRITICAL: PROTECTED DATABASE OPERATIONS

This document contains instructions for safely managing the DASWOS database with built-in protection and backup mechanisms.

## 🔒 SECURITY FEATURES

The protected database schema includes:
- **Automatic backup creation** before any destructive operations
- **Audit logging** of all database operations
- **Safe deletion functions** that create backups first
- **Email notifications** for critical operations
- **Data recovery capabilities** from backup schema

## 📋 SAFE OPERATIONS

### 1. Safe User Data Deletion

To delete a specific user and all their data safely:

```sql
-- This will automatically create a backup before deletion
SELECT safe_delete_user_data('username_here');
```

This function will:
- Create a complete backup of all tables
- Delete all messages, files, projects, and access records for the user
- Delete the user account
- Log the operation in audit_log
- Provide a summary of what was deleted

### 2. Safe Complete Database Wipe

To wipe all user data while preserving structure:

```sql
-- This will automatically create a backup before wiping
SELECT safe_wipe_all_data();
```

This function will:
- Create a timestamped backup of all data
- Truncate all user data tables (preserving structure)
- Log the operation with data counts
- Preserve audit logs for recovery purposes

### 3. Manual Backup Creation

To create a backup without deleting anything:

```sql
-- Create a manual backup
SELECT create_automatic_backup();
```

## 🔍 MONITORING AND AUDITING

### View Recent Operations

```sql
-- See all recent database operations
SELECT * FROM audit_log 
ORDER BY performed_at DESC 
LIMIT 20;
```

### Check Backup Status

```sql
-- See all backups created
SELECT * FROM audit_log 
WHERE operation_type = 'BACKUP' 
ORDER BY performed_at DESC;
```

### View Available Backup Tables

```sql
-- List all backup tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'backup_data' 
ORDER BY table_name;
```

## 🔄 DATA RECOVERY

### Restore from Backup

To restore data from a specific backup (replace YYYY_MM_DD_HH24_MI_SS with actual timestamp):

```sql
-- Example: Restore users table from backup
INSERT INTO public.users 
SELECT * FROM backup_data.users_2024_01_30_14_30_00
ON CONFLICT (username) DO NOTHING;

-- Example: Restore messages table from backup
INSERT INTO public.messages 
SELECT * FROM backup_data.messages_2024_01_30_14_30_00;

-- Example: Restore files table from backup
INSERT INTO public.files 
SELECT * FROM backup_data.files_2024_01_30_14_30_00;
```

### Complete Database Restore

To restore the entire database from a backup:

```sql
-- First, create a backup of current state
SELECT create_automatic_backup();

-- Then restore each table (replace timestamp as needed)
TRUNCATE TABLE users CASCADE;
INSERT INTO users SELECT * FROM backup_data.users_YYYY_MM_DD_HH24_MI_SS;

TRUNCATE TABLE messages CASCADE;
INSERT INTO messages SELECT * FROM backup_data.messages_YYYY_MM_DD_HH24_MI_SS;

TRUNCATE TABLE files CASCADE;
INSERT INTO files SELECT * FROM backup_data.files_YYYY_MM_DD_HH24_MI_SS;

TRUNCATE TABLE projects CASCADE;
INSERT INTO projects SELECT * FROM backup_data.projects_YYYY_MM_DD_HH24_MI_SS;

TRUNCATE TABLE temporary_access CASCADE;
INSERT INTO temporary_access SELECT * FROM backup_data.temporary_access_YYYY_MM_DD_HH24_MI_SS;

-- Continue for other tables as needed...
```

## 🚨 EMERGENCY PROCEDURES

### If Database is Accidentally Wiped

1. **Don't Panic** - All data should be backed up automatically
2. **Check audit log** to see what happened:
   ```sql
   SELECT * FROM audit_log WHERE operation_type IN ('DELETE', 'TRUNCATE') ORDER BY performed_at DESC LIMIT 5;
   ```
3. **Find the most recent backup** before the incident:
   ```sql
   SELECT backup_location, performed_at FROM audit_log WHERE operation_type = 'BACKUP' ORDER BY performed_at DESC LIMIT 5;
   ```
4. **Restore from backup** using the procedures above

### If Backup Schema is Missing

If the backup_data schema is accidentally dropped:

```sql
-- Recreate backup schema
CREATE SCHEMA IF NOT EXISTS backup_data;

-- Recreate backup functions (run the protected-stream.sql file)
```

## 📧 EMAIL NOTIFICATIONS

The system is designed to send email notifications for critical operations to `<EMAIL>`. This requires:

1. **Supabase Edge Functions** or **Database Webhooks** to be configured
2. **Email service integration** (SendGrid, AWS SES, etc.)
3. **Trigger setup** for critical operations

### Setting Up Email Notifications

```sql
-- Example webhook/notification trigger (implementation depends on your setup)
CREATE OR REPLACE FUNCTION notify_critical_operation()
RETURNS TRIGGER AS $$
BEGIN
  -- This would integrate with your email service
  -- Implementation depends on your email provider
  PERFORM pg_notify('critical_operation', 
    json_build_object(
      'operation', NEW.operation_type,
      'table', NEW.table_name,
      'timestamp', NEW.performed_at,
      'backup_location', NEW.backup_location
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for critical operations
CREATE TRIGGER notify_on_critical_ops
  AFTER INSERT ON audit_log
  FOR EACH ROW
  WHEN (NEW.operation_type IN ('DELETE', 'TRUNCATE', 'WIPE'))
  EXECUTE FUNCTION notify_critical_operation();
```

## ⚡ QUICK REFERENCE

| Operation | Command | Backup Created | Reversible |
|-----------|---------|----------------|------------|
| Delete User | `SELECT safe_delete_user_data('username');` | ✅ Yes | ✅ Yes |
| Wipe Database | `SELECT safe_wipe_all_data();` | ✅ Yes | ✅ Yes |
| Manual Backup | `SELECT create_automatic_backup();` | ✅ Yes | N/A |
| View Audit Log | `SELECT * FROM audit_log ORDER BY performed_at DESC;` | ❌ No | N/A |

## 🔧 MAINTENANCE

### Clean Old Backups

To clean backups older than 30 days:

```sql
-- List old backup tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'backup_data' 
AND table_name ~ '_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}$'
AND substring(table_name from '(\d{4}_\d{2}_\d{2})') < to_char(now() - interval '30 days', 'YYYY_MM_DD');

-- Drop old backup tables (run carefully!)
-- DO NOT run this without checking the list above first
```

### Database Health Check

```sql
-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname IN ('public', 'backup_data')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check recent activity
SELECT 
  operation_type,
  COUNT(*) as count,
  MAX(performed_at) as last_occurrence
FROM audit_log 
WHERE performed_at > now() - interval '7 days'
GROUP BY operation_type
ORDER BY last_occurrence DESC;
```

---

**⚠️ IMPORTANT**: Always test recovery procedures in a development environment before applying to production!
