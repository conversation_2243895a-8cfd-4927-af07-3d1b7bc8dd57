const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Read the SQL file
const sql = fs.readFileSync('./database-projects.sql', 'utf8');

// Create Supabase client (you'll need to add your actual URL and key)
const supabaseUrl = 'https://ixqjqjqjqjqjqjqjqjqj.supabase.co';
const supabaseKey = 'your-service-role-key-here'; // Use service role key for admin operations

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    console.log('Running database migration...');
    
    // Split SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement) {
        console.log('Executing:', trimmedStatement.substring(0, 50) + '...');
        const { error } = await supabase.rpc('exec_sql', { sql_query: trimmedStatement });
        if (error) {
          console.error('Error executing statement:', error);
          console.error('Statement was:', trimmedStatement);
        } else {
          console.log('✅ Statement executed successfully');
        }
      }
    }
    
    console.log('Migration completed!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

runMigration();
