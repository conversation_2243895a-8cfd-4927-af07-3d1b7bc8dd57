import React, { useState } from 'react';
import { supabase } from '../lib/supabaseClient';

const RequestAccess = () => {
  const [form, setForm] = useState({ name: '', email: '', company: '' });
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError('');
    // Store request in Supabase (table: access_requests)
    const { error } = await supabase.from('access_requests').insert([
      {
        id: crypto.randomUUID(),
        ...form,
        status: 'pending',
        requested_at: new Date().toISOString(),
        approved_at: null,
        approved_by: null
      }
    ]);
    if (error) {
      setError(`Submission failed: ${error.message}`);
      console.error('Supabase insert error:', error);
    } else {
      setSubmitted(true);
      // Send confirmation email (mailto for now)
      window.open(`mailto:${form.email}?subject=Access%20Request%20Received&body=Thank%20you%20for%20your%20request.%20Our%20team%20will%20review%20it%20and%20contact%20you%20once%20approved.`);
    }
  };

  if (submitted) return <div className="text-center text-green-400">Request submitted! Please check your email for confirmation and wait for verification. Our team will contact you once your access is approved.</div>;

  return (
    <form onSubmit={handleSubmit} className="max-w-sm mx-auto bg-gray-800 p-6 rounded-lg shadow text-xs">
      <h2 className="text-white text-base mb-4 font-bold">Request Access</h2>
      <input name="name" placeholder="Name" value={form.name} onChange={handleChange} className="w-full mb-2 px-3 py-2 rounded bg-gray-900 text-gray-100 border border-gray-700" required />
      <input name="email" placeholder="Email" value={form.email} onChange={handleChange} className="w-full mb-2 px-3 py-2 rounded bg-gray-900 text-gray-100 border border-gray-700" required />
      <input name="company" placeholder="Company" value={form.company} onChange={handleChange} className="w-full mb-4 px-3 py-2 rounded bg-gray-900 text-gray-100 border border-gray-700" required />
      {error && <div className="text-red-400 mb-2">{error}</div>}
      <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded font-semibold">Submit</button>
    </form>
  );
};

export default RequestAccess;
