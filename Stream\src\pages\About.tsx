import React from "react";

const About = () => (
  <section>
    <h1 className="text-xl font-bold text-white mb-2">Who We Are: Your Partners in Operational Excellence</h1>
    <p className="text-gray-300 mb-2 text-xs">Founded in 2025, we're a team of experienced consultants passionate about helping businesses thrive in a data-rich world. We started with hands-on, manual methodologies to deliver immediate value, and we're now building DASWOS—our proprietary Data Analysis Software for Workflow Optimization Systems—as an internal tool to supercharge our analyses.</p>
    <div className="mb-4 text-xs">
      <h2 className="text-base font-semibold text-gray-100 mb-1">What Sets Us Apart</h2>
      <ul className="list-disc pl-4 text-gray-200">
        <li><strong className="text-white">Human-First Approach:</strong> We combine stakeholder interviews, Lean/Six Sigma frameworks, and custom solutions with emerging tech.</li>
        <li><strong className="text-white">DASWOS in Development:</strong> This cutting-edge platform will use AI/ML to automate process discovery, run simulations, and generate prescriptive recommendations—exclusively for our consulting team to deliver unparalleled results. Stay tuned for updates!</li>
        <li><strong className="text-white">Our Commitment:</strong> No off-the-shelf software sales—just trusted, tailored partnerships for lasting efficiency gains.</li>
      </ul>
    </div>
    <div className="mb-4 text-xs">
      <h2 className="text-base font-semibold text-gray-100 mb-1">Our Team</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <div className="bg-gray-800 rounded-lg p-2 text-center">
          <img src="/team-john.jpg" alt="John Doe" className="mx-auto rounded-full h-12 w-12 mb-1 object-cover" />
          <div className="font-bold text-white text-xs">John Doe</div>
          <div className="text-gray-300 text-xs">Founder: 15+ years in operations consulting</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-2 text-center">
          <img src="/team-jane.jpg" alt="Jane Smith" className="mx-auto rounded-full h-12 w-12 mb-1 object-cover" />
          <div className="font-bold text-white text-xs">Jane Smith</div>
          <div className="text-gray-300 text-xs">Lead Analyst: 10+ years in process improvement</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-2 text-center">
          <img src="/team-alex.jpg" alt="Alex Lee" className="mx-auto rounded-full h-12 w-12 mb-1 object-cover" />
          <div className="font-bold text-white text-xs">Alex Lee</div>
          <div className="text-gray-300 text-xs">Tech Lead: 8+ years in workflow automation</div>
        </div>
      </div>
    </div>
    <a href="/contact" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition text-xs">Join our journey—sign up for updates on DASWOS progress.</a>
  </section>
);

export default About;
