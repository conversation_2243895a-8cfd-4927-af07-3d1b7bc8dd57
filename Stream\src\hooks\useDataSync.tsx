// React hook for data synchronization
import React, { useState, useEffect, useCallback } from 'react';
import { dataSyncManager } from '../utils/dataSync';
import { supabase } from '../lib/supabaseClient';

export interface SyncStatus {
  isOnline: boolean;
  unsyncedCount: number;
  lastSync: string | null;
  isRecovering: boolean;
}

export const useDataSync = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: true,
    unsyncedCount: 0,
    lastSync: null,
    isRecovering: false
  });

  // Check database connectivity
  const checkConnectivity = useCallback(async () => {
    try {
      const { error } = await supabase.from('access_requests').select('id').limit(1);
      setSyncStatus(prev => ({ ...prev, isOnline: !error }));
      return !error;
    } catch {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
      return false;
    }
  }, []);

  // Update unsynced count
  const updateUnsyncedCount = useCallback(() => {
    const unsyncedData = dataSyncManager.getUnsyncedData();
    const count = unsyncedData.filter(item => !item.synced).length;
    setSyncStatus(prev => ({ ...prev, unsyncedCount: count }));
  }, []);

  // Force sync now
  const forceSync = useCallback(async () => {
    await dataSyncManager.performSync();
    updateUnsyncedCount();
    setSyncStatus(prev => ({ ...prev, lastSync: new Date().toISOString() }));
  }, [updateUnsyncedCount]);

  // Recover all cached data
  const recoverData = useCallback(async () => {
    setSyncStatus(prev => ({ ...prev, isRecovering: true }));
    try {
      await dataSyncManager.recoverAllData();
      updateUnsyncedCount();
    } finally {
      setSyncStatus(prev => ({ ...prev, isRecovering: false }));
    }
  }, [updateUnsyncedCount]);

  // Safe database operation with automatic caching
  const safeDbOperation = useCallback(async (
    table: string,
    operation: 'insert' | 'update' | 'delete',
    data: any,
    fallbackToCache = true
  ) => {
    try {
      let result;
      
      if (operation === 'insert') {
        result = await supabase.from(table).insert([data]).select();
      } else if (operation === 'update') {
        result = await supabase.from(table).update(data.updates).eq('id', data.id).select();
      } else if (operation === 'delete') {
        result = await supabase.from(table).delete().eq('id', data.id);
      }

      if (result?.error) throw result.error;
      
      return { success: true, data: result?.data };
    } catch (error) {
      console.warn(`Database operation failed for ${table}:`, error);
      
      if (fallbackToCache && operation === 'insert') {
        // Store for later sync
        const id = await dataSyncManager.storeForSync(table, data);
        updateUnsyncedCount();
        
        return { 
          success: true, 
          data: [{ ...data, id }], 
          cached: true 
        };
      }
      
      return { success: false, error };
    }
  }, [updateUnsyncedCount]);

  // Initialize
  useEffect(() => {
    checkConnectivity();
    updateUnsyncedCount();
    
    // Check connectivity every 30 seconds
    const connectivityInterval = setInterval(checkConnectivity, 30000);
    
    // Update unsynced count every 10 seconds
    const countInterval = setInterval(updateUnsyncedCount, 10000);
    
    return () => {
      clearInterval(connectivityInterval);
      clearInterval(countInterval);
    };
  }, [checkConnectivity, updateUnsyncedCount]);

  return {
    syncStatus,
    forceSync,
    recoverData,
    safeDbOperation,
    checkConnectivity,
    updateUnsyncedCount
  };
};

// Component for sync status display
export const SyncStatusIndicator = () => {
  const { syncStatus, forceSync, recoverData } = useDataSync();

  if (syncStatus.unsyncedCount === 0 && syncStatus.isOnline) {
    return (
      <div className="flex items-center text-green-400 text-sm">
        <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
        All data synced
      </div>
    );
  }

  return (
    <div className="bg-yellow-900/50 border border-yellow-600 rounded-lg p-3 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            syncStatus.isOnline ? 'bg-yellow-400' : 'bg-red-400'
          }`}></div>
          <span className="text-yellow-200 text-sm">
            {syncStatus.isOnline 
              ? `${syncStatus.unsyncedCount} items pending sync`
              : 'Database offline - data cached locally'
            }
          </span>
        </div>
        
        <div className="flex gap-2">
          {syncStatus.unsyncedCount > 0 && (
            <button
              onClick={forceSync}
              className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700"
            >
              Sync Now
            </button>
          )}
          
          <button
            onClick={recoverData}
            disabled={syncStatus.isRecovering}
            className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {syncStatus.isRecovering ? 'Recovering...' : 'Recover Data'}
          </button>
        </div>
      </div>
    </div>
  );
};
