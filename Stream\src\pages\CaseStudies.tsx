import React from "react";

const CaseStudies = () => (
  <section>
      <h1 className="text-lg font-extrabold text-white mb-2 drop-shadow-lg opacity-90">Case Studies</h1>
    <div className="bg-gray-800 rounded-lg p-3 mb-4 text-xs">
      <h2 className="text-base font-semibold text-white mb-1">Featured Case Study: Streamlining Construction Operations</h2>
      <ul className="list-disc pl-4 text-gray-200 mb-2">
        <li><strong>Client:</strong> A mid-sized construction company facing workflow inefficiencies, data silos, and slow project tracking.</li>
        <li><strong>Challenge:</strong> Disorganized processes leading to delays, cost overruns, and poor team communication.</li>
        <li><strong>Our Solution:</strong> Manual assessments, mapped workflows, and a custom website/client portal with real-time dashboards, automated reporting, and secure data entry interfaces.</li>
        <li><strong>Results:</strong> Reduced project timelines by 25%, improved data accuracy, and enhanced cross-team collaboration.</li>
      </ul>
      <blockquote className="border-l-4 border-gray-500 pl-2 text-gray-300 italic mb-1">"OptiFlow transformed our operations— their portal is a game-changer!" – Construction CEO</blockquote>
      <div className="text-gray-400 text-xs">More case studies coming soon as we grow. This project exemplifies our ability to deliver quick, tangible wins while we develop DASWOS for advanced analytics.</div>
    </div>
    {/* Contact button removed for exclusivity */}
  </section>
);

export default CaseStudies;
