-- D<PERSON><PERSON>AS<PERSON> WIPE SCRIPT
-- WARNING: This will delete ALL data in your database
-- Use this to completely reset your database before applying the new schema

-- Drop all tables in correct order (respecting foreign key dependencies)
DROP TABLE IF EXISTS files CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS contact_requests CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS access_requests CASCADE;

-- Drop any remaining indexes (they should be dropped with tables, but just in case)
DROP INDEX IF EXISTS idx_access_requests_status;
DROP INDEX IF EXISTS idx_access_requests_email;
DROP INDEX IF EXISTS idx_access_requests_requested_at;
DROP INDEX IF EXISTS idx_users_username;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_is_active;
DROP INDEX IF EXISTS idx_contact_requests_status;
DROP INDEX IF EXISTS idx_contact_requests_submitted_at;
DROP INDEX IF EXISTS idx_messages_sender_username;
DROP INDEX IF EXISTS idx_messages_recipient_username;
DROP INDEX IF EXISTS idx_messages_read_by_admin;
DROP INDEX IF EXISTS idx_messages_read_by_client;
DROP INDEX IF EXISTS idx_messages_created_at;
DROP INDEX IF EXISTS idx_messages_conversation;
DROP INDEX IF EXISTS idx_files_uploader_username;
DROP INDEX IF EXISTS idx_files_target_username;
DROP INDEX IF EXISTS idx_files_reviewed_by_admin;
DROP INDEX IF EXISTS idx_files_uploaded_by_admin;
DROP INDEX IF EXISTS idx_files_read_by_client;
DROP INDEX IF EXISTS idx_files_created_at;
DROP INDEX IF EXISTS idx_files_admin_uploads;

-- Drop any legacy indexes that might exist
DROP INDEX IF EXISTS idx_messages_recipient;
DROP INDEX IF EXISTS idx_files_uploader;

-- Confirm all tables are dropped
SELECT 'Database wiped successfully - all tables and indexes removed' as status;
