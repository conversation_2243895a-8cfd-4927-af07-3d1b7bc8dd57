import { supabase } from '../lib/supabaseClient';

export interface RemovalRequest {
  id: string;
  requester_username: string;
  target_username: string;
  item_type: 'message' | 'file';
  item_id: string;
  reason?: string;
  status: 'pending' | 'approved' | 'denied';
  responded_at?: string;
  request_count?: number;
  created_at: string;
}

export class DeletionManager {
  /**
   * Delete a message from the current user's view only
   * This is a soft delete - the message remains visible to the other party
   */
  static async deleteMessageFromMyView(messageId: string, currentUsername: string): Promise<boolean> {
    try {
      // First, get the message to determine if user is sender or recipient
      const { data: message, error: fetchError } = await supabase
        .from('messages')
        .select('sender_username, recipient_username')
        .eq('id', messageId)
        .single();

      if (fetchError || !message) {
        throw new Error('Message not found');
      }

      // Determine which field to update based on user role
      const updateField = message.sender_username === currentUsername 
        ? 'deleted_by_sender' 
        : 'deleted_by_recipient';

      // Update the appropriate deletion flag
      const { error } = await supabase
        .from('messages')
        .update({ [updateField]: true })
        .eq('id', messageId);

      if (error) throw error;

      console.log(`✅ Message deleted from ${currentUsername}'s view`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete message:', error);
      return false;
    }
  }

  /**
   * Delete a message for everyone (both sender and recipient)
   * Used for error corrections within 30 seconds or when auto-approving removal requests
   */
  static async deleteMessageForEveryone(messageId: string, currentUsername: string): Promise<boolean> {
    try {
      // Update both deletion flags to true
      const { error } = await supabase
        .from('messages')
        .update({
          deleted_by_sender: true,
          deleted_by_recipient: true
        })
        .eq('id', messageId);

      if (error) throw error;

      console.log(`✅ Message ${messageId} deleted for everyone by ${currentUsername}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete message for everyone:', error);
      return false;
    }
  }

  /**
   * Delete a file from the current user's view only
   * This is a soft delete - the file remains visible to the other party
   */
  static async deleteFileFromMyView(fileId: string, currentUsername: string): Promise<boolean> {
    try {
      // First, get the file to determine if user is uploader or target
      const { data: file, error: fetchError } = await supabase
        .from('files')
        .select('uploader_username, target_username')
        .eq('id', fileId)
        .single();

      if (fetchError || !file) {
        throw new Error('File not found');
      }

      // Determine which field to update based on user role
      let updateField: string;
      if (file.uploader_username === currentUsername) {
        updateField = 'deleted_by_uploader';
      } else if (file.target_username === currentUsername) {
        updateField = 'deleted_by_target';
      } else {
        throw new Error('User not authorized to delete this file');
      }

      // Update the appropriate deletion flag
      const { error } = await supabase
        .from('files')
        .update({ [updateField]: true })
        .eq('id', fileId);

      if (error) throw error;

      console.log(`✅ File deleted from ${currentUsername}'s view`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete file:', error);
      return false;
    }
  }

  /**
   * Delete a file for everyone (both uploader and target)
   * Used for error corrections within 30 seconds or when auto-approving removal requests
   */
  static async deleteFileForEveryone(fileId: string, currentUsername: string): Promise<boolean> {
    try {
      // Update both deletion flags to true
      const { error } = await supabase
        .from('files')
        .update({
          deleted_by_uploader: true,
          deleted_by_target: true
        })
        .eq('id', fileId);

      if (error) throw error;

      console.log(`✅ File ${fileId} deleted for everyone by ${currentUsername}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete file for everyone:', error);
      return false;
    }
  }

  /**
   * Check if an item is within the 30-second error correction window
   */
  static isWithinErrorCorrectionWindow(createdAt: string): boolean {
    const createdTime = new Date(createdAt).getTime();
    const currentTime = new Date().getTime();
    const thirtySecondsInMs = 30 * 1000;

    return (currentTime - createdTime) <= thirtySecondsInMs;
  }

  /**
   * Send a removal request to the other party
   * This asks them to also remove the item from their view
   */
  static async sendRemovalRequest(
    itemType: 'message' | 'file',
    itemId: string,
    requesterUsername: string,
    targetUsername: string,
    reason?: string
  ): Promise<boolean> {
    try {
      // Check if a pending request already exists for this item
      const { data: existingPendingRequest } = await supabase
        .from('removal_requests')
        .select('id')
        .eq('item_type', itemType)
        .eq('item_id', itemId)
        .eq('requester_username', requesterUsername)
        .eq('status', 'pending')
        .single();

      if (existingPendingRequest) {
        throw new Error('A removal request for this item is already pending');
      }

      // Check how many times this item has been requested for deletion by this user
      const { data: allRequests } = await supabase
        .from('removal_requests')
        .select('request_count, status')
        .eq('item_type', itemType)
        .eq('item_id', itemId)
        .eq('requester_username', requesterUsername)
        .order('created_at', { ascending: false });

      // Calculate the next request count
      let nextRequestCount = 1;
      if (allRequests && allRequests.length > 0) {
        const lastRequest = allRequests[0];
        nextRequestCount = (lastRequest.request_count || 1) + 1;

        // Check if user has exceeded the limit (max 3 requests: initial + 2 after denial)
        if (nextRequestCount > 3) {
          throw new Error('Maximum number of deletion requests (3) reached for this item');
        }
      }

      // Create the removal request
      const { error } = await supabase
        .from('removal_requests')
        .insert([{
          requester_username: requesterUsername,
          target_username: targetUsername,
          item_type: itemType,
          item_id: itemId,
          reason: reason || null,
          status: 'pending',
          request_count: nextRequestCount
        }]);

      if (error) throw error;

      console.log(`✅ Removal request sent for ${itemType} ${itemId} (attempt ${nextRequestCount}/3)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to send removal request:', error);
      return false;
    }
  }

  /**
   * Check if a user can still make deletion requests for an item
   * Returns the number of remaining requests (0-3)
   */
  static async canMakeRemovalRequest(
    itemType: 'message' | 'file',
    itemId: string,
    requesterUsername: string
  ): Promise<{ canRequest: boolean; remainingRequests: number; reason?: string }> {
    try {
      // Check if there's already a pending request
      const { data: pendingRequest } = await supabase
        .from('removal_requests')
        .select('id')
        .eq('item_type', itemType)
        .eq('item_id', itemId)
        .eq('requester_username', requesterUsername)
        .eq('status', 'pending')
        .single();

      if (pendingRequest) {
        return { canRequest: false, remainingRequests: 0, reason: 'Request already pending' };
      }

      // Check how many requests have been made
      const { data: allRequests } = await supabase
        .from('removal_requests')
        .select('request_count')
        .eq('item_type', itemType)
        .eq('item_id', itemId)
        .eq('requester_username', requesterUsername)
        .order('created_at', { ascending: false });

      let nextRequestCount = 1;
      if (allRequests && allRequests.length > 0) {
        const lastRequest = allRequests[0];
        nextRequestCount = (lastRequest.request_count || 1) + 1;
      }

      const remainingRequests = Math.max(0, 3 - nextRequestCount + 1);
      const canRequest = nextRequestCount <= 3;

      return {
        canRequest,
        remainingRequests,
        reason: canRequest ? undefined : 'Maximum requests reached'
      };
    } catch (error) {
      console.error('❌ Failed to check removal request eligibility:', error);
      return { canRequest: false, remainingRequests: 0, reason: 'Error checking eligibility' };
    }
  }

  /**
   * Respond to a removal request (approve or deny)
   */
  static async respondToRemovalRequest(
    requestId: string,
    response: 'approved' | 'denied',
    currentUsername: string
  ): Promise<{ success: boolean; requesterUsername?: string; itemType?: string }> {
    try {
      // Get the removal request details
      const { data: request, error: fetchError } = await supabase
        .from('removal_requests')
        .select('*')
        .eq('id', requestId)
        .eq('target_username', currentUsername)
        .eq('status', 'pending')
        .single();

      if (fetchError || !request) {
        console.error('Request not found or not authorized:', fetchError);
        return { success: false };
      }

      // Update the request status
      const { error: updateError } = await supabase
        .from('removal_requests')
        .update({
          status: response,
          responded_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) {
        console.error('Failed to update request:', updateError);
        return { success: false };
      }

      // If approved, also delete the item from the target user's view
      if (response === 'approved') {
        if (request.item_type === 'message') {
          await this.deleteMessageFromMyView(request.item_id, currentUsername);
        } else if (request.item_type === 'file') {
          await this.deleteFileFromMyView(request.item_id, currentUsername);
        }
      }

      console.log(`✅ Removal request ${response} for ${request.item_type} ${request.item_id}`);
      return {
        success: true,
        requesterUsername: request.requester_username,
        itemType: request.item_type
      };
    } catch (error) {
      console.error('❌ Failed to respond to removal request:', error);
      return { success: false };
    }
  }

  /**
   * Get pending removal requests for the current user
   */
  static async getPendingRemovalRequests(username: string): Promise<RemovalRequest[]> {
    try {
      const { data, error } = await supabase
        .from('removal_requests')
        .select('*')
        .eq('target_username', username)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Failed to fetch removal requests:', error);
      return [];
    }
  }

  /**
   * Get recent completed removal requests that the user sent (to show notifications)
   */
  static async getRecentCompletedRequests(username: string): Promise<RemovalRequest[]> {
    try {
      // Get requests sent by this user that were completed in the last 24 hours
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data, error } = await supabase
        .from('removal_requests')
        .select('*')
        .eq('requester_username', username)
        .in('status', ['approved', 'denied'])
        .gte('responded_at', twentyFourHoursAgo)
        .order('responded_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Failed to fetch completed removal requests:', error);
      return [];
    }
  }

  /**
   * Check if both parties have deleted an item (for cleanup purposes)
   */
  static async isItemFullyDeleted(itemType: 'message' | 'file', itemId: string): Promise<boolean> {
    try {
      if (itemType === 'message') {
        const { data, error } = await supabase
          .from('messages')
          .select('deleted_by_sender, deleted_by_recipient')
          .eq('id', itemId)
          .single();

        if (error || !data) return false;
        return data.deleted_by_sender && data.deleted_by_recipient;
      } else {
        const { data, error } = await supabase
          .from('files')
          .select('deleted_by_uploader, deleted_by_target')
          .eq('id', itemId)
          .single();

        if (error || !data) return false;
        return data.deleted_by_uploader && data.deleted_by_target;
      }
    } catch (error) {
      console.error('❌ Failed to check deletion status:', error);
      return false;
    }
  }

  /**
   * Permanently delete items that both parties have removed (cleanup function)
   * This should be run periodically by an admin or background job
   */
  static async cleanupFullyDeletedItems(): Promise<{ messages: number; files: number }> {
    try {
      // Delete messages where both parties have deleted
      const { data: deletedMessages, error: messageError } = await supabase
        .from('messages')
        .delete()
        .eq('deleted_by_sender', true)
        .eq('deleted_by_recipient', true)
        .select('id');

      if (messageError) throw messageError;

      // Delete files where both parties have deleted
      const { data: deletedFiles, error: fileError } = await supabase
        .from('files')
        .delete()
        .eq('deleted_by_uploader', true)
        .eq('deleted_by_target', true)
        .select('id');

      if (fileError) throw fileError;

      const messageCount = deletedMessages?.length || 0;
      const fileCount = deletedFiles?.length || 0;

      console.log(`✅ Cleanup completed: ${messageCount} messages, ${fileCount} files permanently deleted`);
      return { messages: messageCount, files: fileCount };
    } catch (error) {
      console.error('❌ Failed to cleanup deleted items:', error);
      return { messages: 0, files: 0 };
    }
  }
}

export default DeletionManager;
