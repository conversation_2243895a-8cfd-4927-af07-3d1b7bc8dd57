import React, { useRef, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const navItems = [
  { name: "Home", path: "/" },
  { name: "About", path: "/about" },
  { name: "Services", path: "/services" },
  { name: "Case Studies", path: "/case-studies" },
  { name: "Blog", path: "/blog" },
  { name: "Contact", path: "/contact" },
  { name: "Client Portal", path: "/client-portal" },
];

const Layout = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation();
  const { loginAdmin, isAdmin } = useAuth();
  const [showModal, setShowModal] = useState(false);
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const logoClickCount = useRef(0);
  const logoClickTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleLogoClick = () => {
    // If already admin, go directly to company portal
    if (isAdmin) {
      window.location.href = "/company-portal";
      return;
    }

    logoClickCount.current += 1;
    if (logoClickTimeout.current) clearTimeout(logoClickTimeout.current);
    logoClickTimeout.current = setTimeout(() => {
      logoClickCount.current = 0;
    }, 800);
    if (logoClickCount.current === 3) {
      setShowModal(true);
      logoClickCount.current = 0;
    }
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === "SODA") {
      // Login as admin using AuthContext
      loginAdmin({
        id: 'admin',
        username: 'admin',
        email: '<EMAIL>',
        name: 'Administrator',
        company: 'DASWOS',
        type: 'admin'
      });
      setShowModal(false);
      setPassword("");
      setError("");
      window.location.href = "/company-portal";
    } else {
      setError("Incorrect password.");
    }
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-black via-gray-900 to-gray-800 text-gray-100 font-sans">
      {/* Sidebar */}
      <aside className="w-32 md:w-48 lg:w-56 flex flex-col items-center py-8 bg-black/90 border-r border-gray-800 min-h-screen shadow-2xl z-20 fixed">
        <img src="/daswosglobal.png" alt="Logo" className="h-16 w-16 mb-8 object-contain cursor-pointer" onClick={handleLogoClick} />
        <nav className="flex flex-col space-y-2 w-full">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`w-full block px-4 py-3 text-left font-bold text-lg rounded-lg transition-all duration-200
                ${location.pathname === item.path ? "bg-white/10 text-white shadow-lg" : "text-gray-200 hover:bg-white/5 hover:text-white"}`}
            >
              {item.name}
            </Link>
          ))}
        </nav>
        <div className="flex-1" />
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
            <form onSubmit={handlePasswordSubmit} className="bg-gray-900 p-6 rounded-lg shadow-xl flex flex-col items-center">
              <h2 className="text-white text-lg mb-2 font-bold">Company Portal Access</h2>
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                className="mb-2 px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700 text-sm"
                placeholder="Enter password"
                autoFocus
              />
              {error && <div className="text-red-400 mb-2 text-xs">{error}</div>}
              <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded font-semibold text-xs">Access</button>
              <button type="button" className="text-gray-400 mt-2 text-xs underline" onClick={() => setShowModal(false)}>Cancel</button>
            </form>
          </div>
        )}
        {/* Contact button removed from sidebar for a cleaner look */}
      </aside>
      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen ml-32 md:ml-48 lg:ml-56">
        <main className="flex-1 flex flex-col items-center px-4 py-8 md:py-12 overflow-auto w-full">
          <div className="w-full max-w-5xl mx-auto">
            {children}
          </div>
        </main>
        <footer className="w-full py-3 px-4 text-center text-gray-500 text-sm bg-black/80 border-t border-gray-700/30 backdrop-blur-lg flex-shrink-0">
          &copy; {new Date().getFullYear()} All rights reserved.
        </footer>
      </div>
    </div>
  );
};

export default Layout;
