import React from 'react';
import { Link } from 'react-router-dom';

const VerificationSuccessPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4">
            <svg 
              className="w-8 h-8 text-white" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Email Verified Successfully!
          </h1>
          <p className="text-gray-300 text-sm">
            Your email address has been verified. Your access request is now pending approval.
          </p>
        </div>

        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h2 className="text-white font-semibold mb-2">What happens next?</h2>
          <ul className="text-gray-300 text-sm space-y-1 text-left">
            <li>• Our team will review your access request</li>
            <li>• You'll receive an email with your login credentials once approved</li>
            <li>• This process typically takes 1-2 business days</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Link 
            to="/client-portal" 
            className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors"
          >
            Return to Portal
          </Link>
          <Link 
            to="/" 
            className="block w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors"
          >
            Go to Homepage
          </Link>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-600">
          <p className="text-gray-400 text-xs">
            Need help? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-400 hover:text-blue-300"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerificationSuccessPage;
