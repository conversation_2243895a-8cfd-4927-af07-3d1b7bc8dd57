-- Update schema to support username-based login system
-- Run this script in your Supabase SQL Editor

-- 1. Add new columns to access_requests table
ALTER TABLE access_requests 
ADD COLUMN IF NOT EXISTS generated_username text,
ADD COLUMN IF NOT EXISTS generated_password text,
ADD COLUMN IF NOT EXISTS rejection_reason text;

-- 2. Update users table to support username-based login
-- First, add the username column if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS username text,
ADD COLUMN IF NOT EXISTS is_active boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS last_login timestamptz;

-- 3. Create contact_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS contact_requests (
  id uuid PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  company text,
  message text NOT NULL,
  status text NOT NULL DEFAULT 'new',
  submitted_at timestamptz NOT NULL DEFAULT now(),
  responded_at timestamptz NULL,
  responded_by text NULL,
  notes text NULL
);

-- 4. Update files table to use username instead of email
ALTER TABLE files 
ADD COLUMN IF NOT EXISTS uploader_username text;

-- 5. Update messages table to support username
ALTER TABLE messages 
ADD COLUMN IF NOT EXISTS sender_username text;

-- 6. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_access_requests_status ON access_requests(status);
CREATE INDEX IF NOT EXISTS idx_access_requests_email ON access_requests(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_contact_requests_status ON contact_requests(status);
CREATE INDEX IF NOT EXISTS idx_contact_requests_submitted_at ON contact_requests(submitted_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_recipient ON messages(recipient_email);
CREATE INDEX IF NOT EXISTS idx_files_uploader ON files(uploader_username);

-- 7. Create a unique constraint on username (after ensuring no duplicates)
-- Note: You may need to populate usernames first before adding this constraint
-- ALTER TABLE users ADD CONSTRAINT users_username_unique UNIQUE (username);

-- 8. Insert a demo user for testing (optional)
INSERT INTO users (username, email, password_hash, name, company, is_active, created_at)
VALUES ('demo_user', '<EMAIL>', 'SODA', 'Demo User', 'DASWOS', true, now())
ON CONFLICT (email) DO NOTHING;

-- 9. Update any existing data (if needed)
-- This is a placeholder - you may need to update existing records
-- UPDATE files SET uploader_username = 'demo_user' WHERE uploader_email = '<EMAIL>';
-- UPDATE messages SET sender_username = 'demo_user' WHERE sender_email = '<EMAIL>';
