
import React, { useState } from "react";
import { supabase } from '../lib/supabaseClient';

const Contact = () => {
  const [form, setForm] = useState({ name: '', email: '', company: '', message: '' });
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      // Save contact request to Supabase (table: contact_requests)
      const { error } = await supabase.from('contact_requests').insert([
        {
          id: crypto.randomUUID(),
          ...form,
          status: 'new',
          submitted_at: new Date().toISOString(),
          responded_at: null,
          responded_by: null,
          notes: null
        }
      ]);

      if (error) {
        setError(`Submission failed: ${error.message}`);
        console.error('Supabase insert error:', error);
      } else {
        setSubmitted(true);
        // Reset form
        setForm({ name: '', email: '', company: '', message: '' });
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Contact form submission error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <section>
      <h1 className="text-xl font-bold text-white mb-2 text-center">Get in Touch: Let's Optimize Together</h1>
      {submitted ? (
        <div className="bg-green-900 text-green-300 text-center rounded p-4 mb-4 max-w-md mx-auto text-sm">
          Message sent! Thank you for reaching out. We will get back to you soon.
        </div>
      ) : (
        <>
          {error && (
            <div className="bg-red-900 text-red-300 text-center rounded p-4 mb-4 max-w-md mx-auto text-sm">
              {error}
            </div>
          )}
          <form className="bg-gray-800 rounded-lg p-4 mb-4 max-w-md mx-auto text-sm" onSubmit={handleSubmit}>
          <div className="mb-2">
            <label htmlFor="name" className="block text-gray-200 mb-0.5 text-xs">Name</label>
            <input type="text" id="name" name="name" value={form.name} onChange={handleChange} className="w-full px-3 py-1 rounded bg-gray-900 text-gray-100 border border-gray-700 focus:outline-none focus:border-blue-400 text-xs" required />
          </div>
          <div className="mb-2">
            <label htmlFor="email" className="block text-gray-200 mb-0.5 text-xs">Email</label>
            <input type="email" id="email" name="email" value={form.email} onChange={handleChange} className="w-full px-3 py-1 rounded bg-gray-900 text-gray-100 border border-gray-700 focus:outline-none focus:border-blue-400 text-xs" required />
          </div>
          <div className="mb-2">
            <label htmlFor="company" className="block text-gray-200 mb-0.5 text-xs">Company</label>
            <input type="text" id="company" name="company" value={form.company} onChange={handleChange} className="w-full px-3 py-1 rounded bg-gray-900 text-gray-100 border border-gray-700 focus:outline-none focus:border-blue-400 text-xs" />
          </div>
          <div className="mb-2">
            <label htmlFor="message" className="block text-gray-200 mb-0.5 text-xs">Message</label>
            <textarea id="message" name="message" rows={3} value={form.message} onChange={handleChange} className="w-full px-3 py-1 rounded bg-gray-900 text-gray-100 border border-gray-700 focus:outline-none focus:border-blue-400 text-xs" required></textarea>
          </div>
          <button
            type="submit"
            disabled={submitting}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? 'Sending...' : 'Send Message'}
          </button>
        </form>
        </>
      )}
      <div className="text-gray-300 text-center mb-2 text-xs">
        Email: <EMAIL> | Phone: +44 (0)20 1234 5678 | Location: London, UK
      </div>
      <div className="text-center text-gray-400 mb-1 text-xs">
        <a href="https://linkedin.com" className="mx-2 text-gray-300 hover:underline">LinkedIn</a>
        <a href="https://twitter.com" className="mx-2 text-gray-300 hover:underline">Twitter</a>
      </div>
      <div className="text-center text-gray-400 text-xs mt-1">
        We respect your data—GDPR compliant.
      </div>
    </section>
  );
};

export default Contact;
