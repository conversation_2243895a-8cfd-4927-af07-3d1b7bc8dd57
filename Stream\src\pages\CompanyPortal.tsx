import React, { useEffect, useState, useRef } from 'react';
import { supabase } from '../lib/supabaseClient';
import { hashPassword, generateSecurePassword, generateUsername } from '../utils/auth';
import emailService, { sendEmailViaBackend } from '../utils/emailService';
import { useAuth } from '../context/AuthContext';
import { useDataSync, SyncStatusIndicator } from '../hooks/useDataSync';
import { adminActivityLogger } from '../utils/adminActivityLogger';
import DeletionManager from '../utils/deletionManager';

type RemovalRequest = {
  id: string;
  requester_username: string;
  target_username: string;
  item_type: 'message' | 'file';
  item_id: string;
  reason?: string;
  status: 'pending' | 'approved' | 'denied';
  responded_at?: string;
  created_at: string;
};

type AccessRequest = {
  id: string;
  name: string;
  email: string;
  company: string;
  status: string;
  requested_at: string;
  approved_at?: string;
  approved_by?: string;
};

type ContactRequest = {
  id: string;
  name: string;
  email: string;
  company?: string;
  message: string;
  status: string;
  submitted_at: string;
  responded_at?: string;
  responded_by?: string;
  notes?: string;
};

const CompanyPortal = () => {
  const { logoutAdmin, isAdmin, activeClientSessions, switchToClientSession, logoutClientSession, loginClient, endTemporaryAccess, clientUser, adminUser } = useAuth();
  const [requests, setRequests] = useState<AccessRequest[]>([]);
  const [contactRequests, setContactRequests] = useState<ContactRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [approving, setApproving] = useState<string | null>(null);
  const [clients, setClients] = useState<any[]>([]);
  const [selectedClient, setSelectedClient] = useState<any | null>(null);
  const [clientMessages, setClientMessages] = useState<any[]>([]);
  const [clientFiles, setClientFiles] = useState<any[]>([]);
  const [loadingClientData, setLoadingClientData] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [replyAttachment, setReplyAttachment] = useState<File | null>(null);
  const [fileComments, setFileComments] = useState<{[key: string]: string}>({});
  const [uploadingToClient, setUploadingToClient] = useState(false);

  // Project management
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState<any | null>(null);
  const [loadingProjects, setLoadingProjects] = useState(false);
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [newProject, setNewProject] = useState({ name: '', description: '' });

  // Removal requests management
  const [pendingRemovalRequests, setPendingRemovalRequests] = useState<RemovalRequest[]>([]);
  const [completedRequests, setCompletedRequests] = useState<RemovalRequest[]>([]);

  // Reason popup management
  const [showReasonPopup, setShowReasonPopup] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>('');

  // Data sync functionality
  const { safeDbOperation, syncStatus } = useDataSync();

  // Temporary access management
  const [temporaryAccess, setTemporaryAccess] = useState<any[]>([]);

  // Dropdown menu states
  const [openMessageDropdown, setOpenMessageDropdown] = useState<string | null>(null);
  const [openFileDropdown, setOpenFileDropdown] = useState<string | null>(null);

  // Refs for auto-scroll functionality
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const filesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [clientMessages]);

  // Auto-scroll to bottom when files change
  useEffect(() => {
    if (filesEndRef.current) {
      filesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [clientFiles]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setOpenMessageDropdown(null);
        setOpenFileDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch project data when selectedProject changes
  useEffect(() => {
    if (selectedProject) {
      fetchProjectData(selectedProject);
    }
  }, [selectedProject]);

  const fetchContactRequests = async () => {
    const { data, error } = await supabase.from('contact_requests').select('*').order('submitted_at', { ascending: false });
    if (!error) setContactRequests(data || []);
  };

  // Temporary migration function - run once to set up projects
  const runProjectsMigration = async () => {
    if (!confirm('This will run the projects migration. Are you sure?')) return;

    try {
      console.log('Running projects migration...');

      // Create projects table
      await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE TABLE IF NOT EXISTS projects (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            name text NOT NULL,
            description text,
            client_username text NOT NULL REFERENCES users(username) ON DELETE CASCADE,
            created_by_admin boolean NOT NULL DEFAULT TRUE,
            status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
            created_at timestamptz NOT NULL DEFAULT now(),
            updated_at timestamptz NOT NULL DEFAULT now()
          );
        `
      });

      // Add project_id columns
      await supabase.rpc('exec_sql', {
        sql_query: 'ALTER TABLE messages ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;'
      });

      await supabase.rpc('exec_sql', {
        sql_query: 'ALTER TABLE files ADD COLUMN IF NOT EXISTS project_id uuid REFERENCES projects(id) ON DELETE SET NULL;'
      });

      await supabase.rpc('exec_sql', {
        sql_query: 'ALTER TABLE files ADD COLUMN IF NOT EXISTS client_comment text;'
      });

      // Create indexes
      await supabase.rpc('exec_sql', {
        sql_query: 'CREATE INDEX IF NOT EXISTS idx_projects_client_username ON projects(client_username);'
      });

      await supabase.rpc('exec_sql', {
        sql_query: 'CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);'
      });

      await supabase.rpc('exec_sql', {
        sql_query: 'CREATE INDEX IF NOT EXISTS idx_files_project_id ON files(project_id);'
      });

      alert('✅ Migration completed successfully!');
    } catch (error) {
      console.error('Migration failed:', error);
      alert('❌ Migration failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  useEffect(() => {
    const fetchRequests = async () => {
      setLoading(true);
      // Only fetch verified requests (email_verified_at is not null) or already processed requests
      const { data, error } = await supabase
        .from('access_requests')
        .select('*')
        .or('email_verified_at.not.is.null,status.eq.approved,status.eq.rejected')
        .order('requested_at', { ascending: false });
      if (error) {
        setError('Failed to load requests.');
        setRequests([]);
      } else {
        setRequests(data || []);
      }
      setLoading(false);
    };
    fetchRequests();
    fetchContactRequests();
    fetchClients();
    fetchTemporaryAccess();
  }, []);

  // Fetch temporary access grants
  const fetchTemporaryAccess = async () => {
    try {
      // Fetch active access grants
      const { data, error } = await supabase
        .from('temporary_access')
        .select('*')
        .eq('status', 'active')
        .gt('expires_at', new Date().toISOString())
        .order('granted_by_client_at', { ascending: false });

      if (error) throw error;
      setTemporaryAccess(data || []);

      // Also fetch recently revoked access (within last 24 hours) for reference
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { data: revokedData, error: revokedError } = await supabase
        .from('temporary_access')
        .select('*')
        .eq('status', 'revoked')
        .gte('revoked_at', twentyFourHoursAgo)
        .order('revoked_at', { ascending: false });

      if (!revokedError && revokedData && revokedData.length > 0) {
        console.log('📋 Recently revoked access grants:', revokedData);
        // You could set this to state if you want to display it in the UI
      }

    } catch (error) {
      console.error('Error fetching temporary access:', error);
    }
  };

  const fetchClients = async () => {
    try {
      // Fetch all approved users (clients)
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, username, email, name, company, created_at, last_login')
        .eq('is_active', true)
        .order('last_login', { ascending: false });

      if (usersError) throw usersError;

      // For each client, get their message and file counts
      const clientsWithCounts = await Promise.all(
        (usersData || []).map(async (user) => {
          // Count unread messages from this client
          const { count: messageCount } = await supabase
            .from('messages')
            .select('*', { count: 'exact', head: true })
            .eq('sender_username', user.username)
            .eq('read_by_admin', false);

          // Count unreviewed files uploaded by this client
          const { count: fileCount } = await supabase
            .from('files')
            .select('*', { count: 'exact', head: true })
            .eq('uploader_username', user.username)
            .eq('reviewed_by_admin', false);

          return {
            ...user,
            unreadMessages: messageCount || 0,
            newFiles: fileCount || 0
          };
        })
      );

      setClients(clientsWithCounts);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  // Use temporary access to login as client
  const useTemporaryAccess = async (accessGrant: any) => {
    try {
      console.log('🔑 Starting temporary access for:', accessGrant.client_username);

      // First, verify the access is still valid and active
      const { data: currentAccess, error: checkError } = await supabase
        .from('temporary_access')
        .select('*')
        .eq('id', accessGrant.id)
        .eq('status', 'active')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (checkError || !currentAccess) {
        console.error('❌ Access grant is no longer valid:', checkError);
        alert('❌ This access grant is no longer valid. It may have been revoked or expired.');
        await fetchTemporaryAccess(); // Refresh the list
        return;
      }

      // Mark admin as currently accessing and update last_used_at
      const { data: updateResult, error: updateError } = await supabase
        .from('temporary_access')
        .update({
          last_used_at: new Date().toISOString(),
          admin_currently_accessing: true,
          admin_session_started_at: new Date().toISOString()
        })
        .eq('id', accessGrant.id)
        .select();

      console.log('🔑 Database update result:', { updateResult, updateError });

      if (updateError) {
        console.error('❌ Failed to update temporary access status:', updateError);
        alert('Failed to update access status. The database may need to be updated with new columns.');
        return;
      }

      // Find the user data for this client
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('username', accessGrant.client_username)
        .single();

      if (userError || !userData) {
        throw new Error('Client user not found');
      }

      console.log('🔑 Using temporary access for:', accessGrant.client_username);

      // Create a proper user session object
      const userSession = {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        name: userData.name,
        company: userData.company,
        type: 'client' as const,
        isTemporaryAccess: true,
        temporaryAccessId: accessGrant.id
      };

      // Initialize admin activity logging for this session
      adminActivityLogger.initializeSession(
        accessGrant.id,
        userData.username,
        adminUser?.username || 'Admin'
      );

      // Login as this client using the auth context
      loginClient(userSession);

      // Navigate to client portal
      window.open('/client-portal', '_blank');

    } catch (error) {
      console.error('❌ Failed to use temporary access:', error);
      alert(`❌ FAILED TO ACCESS CLIENT ACCOUNT\n\nError: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Revoke temporary access as admin
  const revokeTemporaryAccessAsAdmin = async (accessGrant: any) => {
    if (!confirm(`Are you sure you want to revoke access to ${accessGrant.client_username}'s account?\n\nThis will immediately end any active admin sessions.`)) {
      return;
    }

    try {
      const revokedAt = new Date().toISOString();

      const { error } = await supabase
        .from('temporary_access')
        .update({
          status: 'revoked',
          revoked_at: revokedAt,
          revoked_by: 'admin',
          admin_currently_accessing: false,
          admin_session_ended_at: revokedAt
        })
        .eq('id', accessGrant.id);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ Temporary access revoked by admin');

      // Refresh the temporary access list
      await fetchTemporaryAccess();

      // Show success message
      alert(`✅ Access to ${accessGrant.client_username}'s account has been revoked.\n\nThe client will be notified of this action.`);

      // Send notification email to client
      try {
        const { data: clientData, error: clientError } = await supabase
          .from('users')
          .select('email, name')
          .eq('username', accessGrant.client_username)
          .single();

        if (!clientError && clientData) {
          const subject = 'Admin Access Revoked - DASWOS';
          const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1f2937;">Admin Access Revoked</h2>
              <p>Hello ${clientData.name},</p>
              <p>The admin has revoked their temporary access to your account.</p>
              <p><strong>Revoked at:</strong> ${new Date(revokedAt).toLocaleString()}</p>
              <p><strong>Original expiration:</strong> ${new Date(accessGrant.expires_at).toLocaleString()}</p>
              <p>Your account is now secure and only accessible by you.</p>
              <p>Best regards,<br>DASWOS Team</p>
            </div>
          `;
          const text = `Admin Access Revoked - Hello ${clientData.name}, the admin has revoked their temporary access to your account. Revoked at: ${new Date(revokedAt).toLocaleString()}. Your account is now secure and only accessible by you.`;

          await sendEmailViaBackend(clientData.email, subject, html, text);
          console.log('✅ Client notified of admin revocation');
        }
      } catch (emailError) {
        console.error('❌ Failed to send revocation email:', emailError);
        // Don't block the revocation if email fails
      }

    } catch (error) {
      console.error('❌ Failed to revoke access:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`❌ FAILED TO REVOKE ACCESS\n\nError: ${errorMessage}\n\nPlease try again.`);
    }
  };

  // Fetch projects for selected client
  const fetchProjects = async (clientUsername: string) => {
    setLoadingProjects(true);
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('client_username', clientUsername)
        .order('created_at', { ascending: true });

      if (error) throw error;

      setProjects(data || []);

      // Auto-select first project if none selected
      if (data && data.length > 0 && !selectedProject) {
        setSelectedProject(data[0]);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      setProjects([]);
    } finally {
      setLoadingProjects(false);
    }
  };

  // Create new project for client
  const createProject = async () => {
    if (!selectedClient || !newProject.name.trim()) return;

    try {
      const { data, error } = await supabase
        .from('projects')
        .insert([{
          name: newProject.name.trim(),
          description: newProject.description.trim() || null,
          client_username: selectedClient.username,
          created_by_admin: true,
          status: 'active'
        }])
        .select();

      if (error) throw error;

      if (data && data.length > 0) {
        setProjects(prev => [...prev, data[0]]);
        setNewProject({ name: '', description: '' });
        setShowCreateProject(false);

        // Auto-select the new project
        setSelectedProject(data[0]);

        alert('✅ Project created successfully!');
      }
    } catch (error) {
      console.error('Error creating project:', error);
      alert('❌ Failed to create project');
    }
  };

  // Fetch messages and files for selected project
  const fetchProjectData = async (project: any) => {
    if (!selectedClient || !project) return;

    setLoadingData(true);
    try {
      // Fetch messages for this project
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('project_id', project.id)
        .order('created_at', { ascending: true });

      if (messagesError) throw messagesError;

      // Filter out messages deleted by admin
      const visibleMessages = (messagesData || []).filter(msg => {
        if (msg.sender_username === 'admin') {
          return !msg.deleted_by_sender;
        } else {
          return !msg.deleted_by_recipient;
        }
      });

      setClientMessages(visibleMessages);

      // Mark messages as read by admin
      await supabase
        .from('messages')
        .update({ read_by_admin: true })
        .eq('project_id', project.id)
        .eq('sender_username', selectedClient.username)
        .eq('read_by_admin', false);

      // Fetch files for this project
      const { data: filesData, error: filesError } = await supabase
        .from('files')
        .select('*')
        .eq('project_id', project.id)
        .order('created_at', { ascending: true });

      if (filesError) throw filesError;

      // Filter out files deleted by admin
      const visibleFiles = (filesData || []).filter(file => {
        if (file.uploader_username === 'admin') {
          return !file.deleted_by_uploader;
        } else if (file.target_username === 'admin') {
          return !file.deleted_by_target;
        }
        return true;
      });

      setClientFiles(visibleFiles);

      // Mark files as reviewed by admin
      await supabase
        .from('files')
        .update({ reviewed_by_admin: true })
        .eq('project_id', project.id)
        .eq('uploader_username', selectedClient.username)
        .eq('reviewed_by_admin', false);

    } catch (error) {
      console.error('Error fetching project data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const selectClient = async (client: any) => {
    setSelectedClient(client);
    setSelectedProject(null); // Reset project selection
    setLoadingClientData(true);

    try {
      // Fetch projects for this client
      await fetchProjects(client.username);

      // Don't fetch messages/files yet - wait for project selection
      setClientMessages([]);
      setClientFiles([]);

      // Fetch removal requests
      fetchRemovalRequests();

      // Refresh client list to update counts
      fetchClients();
    } catch (error) {
      console.error('Error fetching client data:', error);
    } finally {
      setLoadingClientData(false);
    }
  };

  // Send reply to client (STRICT - only succeed if database confirms)
  const sendReply = async () => {
    if ((!replyContent.trim() && !replyAttachment) || !selectedClient || !selectedProject) return;

    // Show loading state
    setLoadingData(true);

    try {
      let fileUrl = '';
      let originalFileName = '';

      // Upload file first if there's an attachment (using same approach as existing file upload)
      if (replyAttachment) {
        originalFileName = replyAttachment.name;
        fileUrl = `/uploads/${originalFileName}`; // Placeholder URL like existing system

        // Save file record to database using same structure as existing uploadFileToClient
        const { error: fileDbError } = await supabase
          .from('files')
          .insert([{
            uploader_username: 'admin',
            filename: originalFileName,
            file_size: replyAttachment.size,
            file_type: replyAttachment.type,
            url: fileUrl,
            uploaded_by_admin: true,
            target_username: selectedClient.username,
            read_by_client: false,
            reviewed_by_admin: true,
            project_id: selectedProject.id,
            created_at: new Date().toISOString()
          }]);

        if (fileDbError) {
          throw new Error(`File database record failed: ${fileDbError.message}`);
        }
      }

      // Prepare message content with file link if attached
      let finalMessageContent = replyContent;
      if (fileUrl && originalFileName) {
        finalMessageContent = replyContent
          ? `${replyContent}\n\n📎 Attached file: ${originalFileName}`
          : `📎 Attached file: ${originalFileName}`;
      }

      // STRICT DATABASE OPERATION - Must confirm in database
      const { data, error } = await supabase
        .from('messages')
        .insert([{
          sender_username: 'admin',
          recipient_username: selectedClient.username,
          content: finalMessageContent,
          read_by_admin: true,
          read_by_client: false, // Client hasn't seen this yet
          project_id: selectedProject.id,
          created_at: new Date().toISOString()
        }])
        .select();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('Reply was not saved to database');
      }

      console.log('✅ Reply confirmed in database:', data[0]);
      setReplyContent('');
      setReplyAttachment(null);

      // Refresh project data
      await fetchProjectData(selectedProject);

      // Show success feedback
      alert('✅ Reply sent successfully!');

    } catch (error) {
      console.error('❌ Reply send failed:', error);

      // Show clear error to user
      alert(`❌ FAILED TO SEND REPLY\n\nError: ${error instanceof Error ? error.message : 'Unknown error'}\n\nYour reply was NOT saved. Please try again when the connection is stable.`);

    } finally {
      setLoadingData(false);
    }
  };

  // Add comment to file
  const addFileComment = async (fileId: string) => {
    const comment = fileComments[fileId];
    if (!comment?.trim()) return;

    try {
      const { error } = await supabase
        .from('files')
        .update({ admin_comment: comment })
        .eq('id', fileId);

      if (error) throw error;

      // Clear comment input
      setFileComments(prev => ({ ...prev, [fileId]: '' }));

      // Refresh files for selected client
      if (selectedClient) {
        const { data: filesData } = await supabase
          .from('files')
          .select('*')
          .eq('uploader_username', selectedClient.username)
          .order('created_at', { ascending: true });

        setClientFiles(filesData || []);
      }
    } catch (error) {
      console.error('Error adding file comment:', error);
    }
  };

  // Upload file to client (STRICT - only succeed if database confirms)
  const uploadFileToClient = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !selectedClient || !selectedProject) return;

    setUploadingToClient(true);

    try {
      // STRICT DATABASE OPERATION - Must confirm in database
      const { data, error } = await supabase
        .from('files')
        .insert([{
          uploader_username: 'admin',
          filename: file.name,
          file_size: file.size,
          file_type: file.type,
          url: `/uploads/${file.name}`, // Placeholder - would be actual storage URL
          uploaded_by_admin: true,
          target_username: selectedClient.username,
          read_by_client: false, // Client hasn't seen this yet
          reviewed_by_admin: true, // Admin uploaded it, so it's "reviewed"
          project_id: selectedProject.id,
          created_at: new Date().toISOString()
        }])
        .select();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('File was not saved to database');
      }

      console.log('✅ File confirmed in database:', data[0]);

      // Clear the file input
      e.target.value = '';

      // Refresh project data
      await fetchProjectData(selectedProject);

      // Show success feedback
      alert(`✅ File "${file.name}" uploaded to ${selectedClient.username} successfully!`);

    } catch (error) {
      console.error('❌ File upload failed:', error);

      // Show clear error to user
      alert(`❌ FAILED TO UPLOAD FILE\n\nFile: ${file.name}\nClient: ${selectedClient.username}\nError: ${error instanceof Error ? error.message : 'Unknown error'}\n\nYour file was NOT saved. Please try again when the connection is stable.`);

      // Don't clear the file input on failure so user can retry

    } finally {
      setUploadingToClient(false);
    }
  };

  const approveRequest = async (id: string, email: string) => {
    setApproving(id);

    // Find the request to get user details
    const request = requests.find(r => r.id === id);
    if (!request) {
      setError('Request not found');
      setApproving(null);
      return;
    }

    // Generate username and password
    const generatedUsername = generateUsername(request.name, email);
    const generatedPassword = generateSecurePassword();

    try {
      // Hash the password before storing
      const hashedPassword = await hashPassword(generatedPassword);

      // First, create user in the users table (with sync fallback)
      const userResult = await safeDbOperation('users', 'insert', {
        id: crypto.randomUUID(),
        username: generatedUsername,
        email: email,
        password_hash: hashedPassword, // Now properly hashed
        name: request.name,
        company: request.company,
        is_active: true,
        created_at: new Date().toISOString()
      });

      if (!userResult.success) {
        setError('Failed to create user: ' + (userResult.error && typeof userResult.error === 'object' && 'message' in userResult.error ? userResult.error.message : 'Unknown error'));
        setApproving(null);
        return;
      }

      if (userResult.cached) {
        console.log('User creation cached - will sync when database is available');
      }

      // Mark as approved in access_requests table with generated credentials (with sync fallback)
      const updateResult = await safeDbOperation('access_requests', 'update', {
        id: id,
        updates: {
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: 'admin',
          generated_username: generatedUsername,
          generated_password: generatedPassword
        }
      });

      if (updateResult.success) {
        setRequests(reqs => reqs.map((r: AccessRequest) =>
          r.id === id ? {
            ...r,
            status: 'approved',
            approved_at: new Date().toISOString(),
            approved_by: 'admin'
          } : r
        ));

        // Send email with credentials using real email service
        try {
          console.log('Sending approval email...');
          const emailSent = await emailService.sendApprovalEmail(
            email,
            generatedUsername,
            generatedPassword
          );

          if (!emailSent) {
            console.error('Approval email service returned false');
            setError('Failed to send approval email. The user has been approved but email notification failed.');
          } else {
            console.log('Approval email sent successfully');
          }
        } catch (emailError) {
          console.error('Approval email failed:', emailError);
          setError('Failed to send approval email. The user has been approved but email notification failed.');
        }
      } else {
        setError('Failed to update request: ' + (updateResult.error && typeof updateResult.error === 'object' && 'message' in updateResult.error ? updateResult.error.message : 'Unknown error'));
      }
    } catch (err) {
      setError('An error occurred during approval: ' + (err as Error).message);
    }

    setApproving(null);
  };

  const rejectRequest = async (id: string, reason: string = 'No reason provided') => {
    const { error } = await supabase.from('access_requests').update({
      status: 'rejected',
      approved_at: new Date().toISOString(),
      approved_by: 'admin',
      rejection_reason: reason
    }).eq('id', id);

    if (!error) {
      setRequests(reqs => reqs.map((r: AccessRequest) =>
        r.id === id ? {
          ...r,
          status: 'rejected',
          approved_at: new Date().toISOString(),
          approved_by: 'admin'
        } : r
      ));
    }
  };

  // Copy message content to clipboard
  const copyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      alert('✅ Message copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy message:', error);
      alert('❌ Failed to copy message');
    }
  };

  // Copy file URL or name to clipboard
  const copyFile = async (file: any) => {
    try {
      const textToCopy = file.url || file.filename;
      await navigator.clipboard.writeText(textToCopy);
      alert('✅ File info copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy file info:', error);
      alert('❌ Failed to copy file info');
    }
  };

  // Request deletion from client
  const requestDeletion = async (itemType: 'message' | 'file', itemId: string, reason?: string) => {
    if (!selectedClient) return;

    try {
      const { error } = await supabase
        .from('removal_requests')
        .insert([{
          requester_username: 'admin',
          target_username: selectedClient.username,
          item_type: itemType,
          item_id: itemId,
          reason: reason || '',
          status: 'pending'
        }]);

      if (error) throw error;

      await fetchRemovalRequests();
      alert('✅ Deletion request sent to client!');
    } catch (error) {
      console.error('Error requesting deletion:', error);
      alert('❌ Failed to send deletion request');
    }
  };

  // Delete message from admin's view or for everyone (only if not in temporary access mode)
  const deleteMessage = async (messageId: string, forEveryone: boolean = false) => {
    // Check if admin is in temporary access mode
    if (clientUser?.isTemporaryAccess) {
      alert('❌ Cannot delete messages while in temporary access mode');
      return;
    }

    // Check if there's a pending deletion request for this message
    const incomingRequest = getPendingRequestForItem('message', messageId);

    let confirmed: boolean;
    let success: boolean;

    if (forEveryone) {
      confirmed = window.confirm('Delete this message for everyone? This cannot be undone.');
      if (!confirmed) return;

      success = await DeletionManager.deleteMessageForEveryone(messageId, 'admin');
      if (success) {
        // If there was a pending deletion request, auto-approve it
        if (incomingRequest) {
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', 'admin');
        }
        // Refresh messages and removal requests
        if (selectedClient) {
          await selectClient(selectedClient);
        }
        fetchRemovalRequests();
        alert('✅ Message deleted for everyone');
      } else {
        alert('❌ Failed to delete message');
      }
    } else {
      confirmed = window.confirm('Delete this message from your view? The client will still see it unless they delete it too.');
      if (!confirmed) return;

      success = await DeletionManager.deleteMessageFromMyView(messageId, 'admin');
      if (success) {
        // If there was a pending deletion request, auto-approve it since the message was deleted
        if (incomingRequest) {
          // Auto-approve the deletion request since the message was deleted
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', 'admin');
          // Also delete for everyone since the request was to remove it from both sides
          await DeletionManager.deleteMessageForEveryone(messageId, 'admin');
        }

        // Refresh messages and removal requests
        if (selectedClient) {
          await selectClient(selectedClient);
        }
        fetchRemovalRequests();
        alert('✅ Message deleted from your view');
      } else {
        alert('❌ Failed to delete message');
      }
    }
  };

  // Delete file from admin's view or for everyone (only if not in temporary access mode)
  const deleteFile = async (fileId: string, forEveryone: boolean = false) => {
    // Check if admin is in temporary access mode
    if (clientUser?.isTemporaryAccess) {
      alert('❌ Cannot delete files while in temporary access mode');
      return;
    }

    // Check if there's a pending deletion request for this file
    const incomingRequest = getPendingRequestForItem('file', fileId);

    let confirmed: boolean;
    let success: boolean;

    if (forEveryone) {
      confirmed = window.confirm('Delete this file for everyone? This cannot be undone.');
      if (!confirmed) return;

      success = await DeletionManager.deleteFileForEveryone(fileId, 'admin');
      if (success) {
        // If there was a pending deletion request, auto-approve it
        if (incomingRequest) {
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', 'admin');
        }

        // Log the deletion activity
        try {
          const { data: file } = await supabase
            .from('files')
            .select('filename')
            .eq('id', fileId)
            .single();

          if (file) {
            await adminActivityLogger.logFileDeleted(file.filename, fileId);
          }
        } catch (error) {
          console.error('Failed to log file deletion:', error);
        }

        // Refresh files and removal requests
        if (selectedClient) {
          await selectClient(selectedClient);
        }
        fetchRemovalRequests();
        alert('✅ File deleted for everyone');
      } else {
        alert('❌ Failed to delete file');
      }
    } else {
      confirmed = window.confirm('Delete this file from your view? The client will still see it unless they delete it too.');
      if (!confirmed) return;

      success = await DeletionManager.deleteFileFromMyView(fileId, 'admin');
      if (success) {
        // If there was a pending deletion request, auto-approve it since the file was deleted
        if (incomingRequest) {
          // Auto-approve the deletion request since the file was deleted
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', 'admin');
          // Also delete for everyone since the request was to remove it from both sides
          await DeletionManager.deleteFileForEveryone(fileId, 'admin');
        }

        // Log the deletion activity if admin is deleting their own content (not during temporary access)
        try {
          const { data: file } = await supabase
            .from('files')
            .select('filename')
            .eq('id', fileId)
            .single();

          if (file) {
            await adminActivityLogger.logFileDeleted(file.filename, fileId);
          }
        } catch (error) {
          console.error('Failed to log file deletion:', error);
        }

        // Refresh files and removal requests
        if (selectedClient) {
          await selectClient(selectedClient);
        }
        fetchRemovalRequests();
        alert('✅ File deleted from your view');
      } else {
        alert('❌ Failed to delete file');
      }
    }
  };

  // Send removal request to client
  const sendRemovalRequest = async (itemType: 'message' | 'file', itemId: string, targetUsername: string) => {
    // Check if admin is in temporary access mode
    if (clientUser?.isTemporaryAccess) {
      alert('❌ Cannot send removal requests while in temporary access mode');
      return;
    }

    // Check if admin can still make requests for this item
    const eligibility = await DeletionManager.canMakeRemovalRequest(itemType, itemId, 'admin');

    if (!eligibility.canRequest) {
      alert(`❌ ${eligibility.reason || 'Cannot make more requests for this item'}`);
      return;
    }

    const reason = prompt(`Optional: Why do you want the client to also remove this ${itemType}?\n\n(${eligibility.remainingRequests} requests remaining after this one)`);

    const success = await DeletionManager.sendRemovalRequest(
      itemType,
      itemId,
      'admin',
      targetUsername,
      reason || undefined
    );

    if (success) {
      fetchRemovalRequests(); // Refresh to show updated state
      alert('✅ Removal request sent to client');
    } else {
      alert('❌ Failed to send removal request');
    }
  };

  // Fetch pending removal requests for admin
  const fetchRemovalRequests = async () => {
    try {
      const requests = await DeletionManager.getPendingRemovalRequests('admin');
      setPendingRemovalRequests(requests);

      // Also fetch completed requests for notifications
      const completed = await DeletionManager.getRecentCompletedRequests('admin');
      setCompletedRequests(completed);
    } catch (error) {
      console.error('Failed to fetch removal requests:', error);
    }
  };

  // Respond to removal request
  const respondToRemovalRequest = async (requestId: string, response: 'approved' | 'denied') => {
    const result = await DeletionManager.respondToRemovalRequest(requestId, response, 'admin');
    if (result.success) {
      fetchRemovalRequests(); // Refresh requests
      if (selectedClient) {
        await selectClient(selectedClient); // Refresh client data
      }

      const actionText = response === 'approved' ? 'approved and item removed' : 'denied';
      const requesterText = result.requesterUsername || 'Client';
      alert(`✅ Deletion request from ${requesterText} ${actionText}. They have been notified.`);
    } else {
      alert('❌ Failed to respond to request');
    }
  };

  // Check if there's a pending removal request for an item (incoming)
  const getPendingRequestForItem = (itemType: 'message' | 'file', itemId: string) => {
    return pendingRemovalRequests.find(req =>
      req.item_type === itemType &&
      req.item_id === itemId &&
      req.status === 'pending' &&
      req.target_username === 'admin'
    );
  };

  // Check if there's an outgoing removal request for an item
  const getOutgoingRequestForItem = (itemType: 'message' | 'file', itemId: string) => {
    return pendingRemovalRequests.find(req =>
      req.item_type === itemType &&
      req.item_id === itemId &&
      req.status === 'pending' &&
      req.requester_username === 'admin'
    );
  };

  const markContactAsResponded = async (id: string, email: string) => {
    const { error } = await supabase.from('contact_requests').update({
      status: 'responded',
      responded_at: new Date().toISOString(),
      responded_by: 'admin'
    }).eq('id', id);

    if (!error) {
      setContactRequests(reqs => reqs.map((r: ContactRequest) =>
        r.id === id ? { ...r, status: 'responded', responded_at: new Date().toISOString(), responded_by: 'admin' } : r
      ));
      // Open email client to respond
      window.open(`mailto:${email}?subject=Re:%20Your%20Contact%20Request&body=Thank%20you%20for%20contacting%20us.%20`);
    }
  };

  // Check if user is authenticated as admin
  if (!isAdmin) {
    return (
      <div className="max-w-sm mx-auto p-6 bg-gray-900 rounded-lg shadow text-xs mt-8">
        <h1 className="text-white text-xl font-bold mb-4 text-center">Access Denied</h1>
        <p className="text-gray-300 text-center">You need admin privileges to access this portal.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-800 overflow-y-auto">
      <div className="max-w-6xl mx-auto p-6 bg-gray-900 shadow text-xs">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-white text-xl font-bold">Admin Portal</h1>
          <p className="text-gray-300">Manage client access requests, contact form submissions, and interact with client portals.</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={runProjectsMigration}
            className="px-3 py-2 bg-purple-600 text-white rounded-lg font-semibold shadow hover:bg-purple-700 transition text-xs"
          >
            Run Projects Migration
          </button>
          <button
            onClick={logoutAdmin}
            className="px-4 py-2 bg-red-600 text-white rounded-lg font-semibold shadow hover:bg-red-700 transition text-xs"
          >
            Logout
          </button>
        </div>
      </div>

      {/* Notifications for completed deletion requests */}
      {completedRequests.length > 0 && (
        <div className="mb-4 p-3 bg-blue-900 rounded border border-blue-700">
          <h3 className="text-sm font-semibold text-blue-200 mb-2">Recent Deletion Request Updates:</h3>
          {completedRequests.map((request) => (
            <div key={request.id} className="text-xs text-blue-100 mb-1">
              Your deletion request for {request.item_type} was <span className={request.status === 'approved' ? 'text-green-300' : 'text-red-300'}>{request.status}</span> by {request.target_username}
              {request.status === 'approved' && ' - Item has been removed from both accounts'}
            </div>
          ))}
          <button
            onClick={() => setCompletedRequests([])}
            className="text-xs text-blue-300 underline mt-1"
          >
            Clear notifications
          </button>
        </div>
      )}

      {/* Data Sync Status */}
      <SyncStatusIndicator />

      {/* Access Requests Section */}
      <h2 className="text-white text-base mb-2 font-bold">Portal Access Requests</h2>
      {loading ? (
        <div className="text-gray-400">Loading requests...</div>
      ) : error ? (
        <div className="text-red-400">{error}</div>
      ) : (
        <table className="w-full text-xs bg-gray-800 rounded">
          <thead>
            <tr className="text-gray-400">
              <th className="p-2">Name</th>
              <th className="p-2">Email</th>
              <th className="p-2">Company</th>
              <th className="p-2">Status</th>
              <th className="p-2">Action</th>
            </tr>
          </thead>
          <tbody>
            {requests.map((req: any) => (
              <tr key={req.id} className={
                req.status === 'approved' ? 'bg-green-900/20' :
                req.status === 'rejected' ? 'bg-red-900/20' : ''
              }>
                <td className="p-2">{req.name}</td>
                <td className="p-2">{req.email}</td>
                <td className="p-2">{req.company}</td>
                <td className="p-2">
                  <span className={`px-2 py-1 rounded text-xs capitalize ${
                    req.status === 'approved' ? 'bg-green-600 text-white' :
                    req.status === 'rejected' ? 'bg-red-600 text-white' :
                    'bg-yellow-600 text-white'
                  }`}>
                    {req.status}
                  </span>
                </td>
                <td className="p-2">
                  {req.status === 'pending' ? (
                    <div className="flex gap-1">
                      <button
                        className="bg-green-600 text-white px-2 py-1 rounded text-xs font-semibold disabled:opacity-50 hover:bg-green-700"
                        onClick={() => approveRequest(req.id, req.email)}
                        disabled={!!approving}
                      >
                        {approving === req.id ? 'Approving...' : 'Approve'}
                      </button>
                      <button
                        className="bg-red-600 text-white px-2 py-1 rounded text-xs font-semibold hover:bg-red-700"
                        onClick={() => rejectRequest(req.id, 'Request denied by admin')}
                        disabled={!!approving}
                      >
                        Reject
                      </button>
                    </div>
                  ) : req.status === 'approved' ? (
                    <span className="text-green-400 text-xs">✓ Approved</span>
                  ) : (
                    <span className="text-red-400 text-xs">✗ Rejected</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {/* Contact Requests Section */}
      <div className="mt-8">
        <h2 className="text-white text-base mb-2 font-bold">Contact Form Submissions</h2>
        {loadingData ? (
          <div className="text-gray-400">Loading contact requests...</div>
        ) : (
          <div className="bg-gray-800 rounded p-2">
            {contactRequests.length === 0 ? (
              <div className="text-gray-400">No contact requests yet.</div>
            ) : (
              <div className="space-y-2">
                {contactRequests.map((req: ContactRequest) => (
                  <div key={req.id} className={`p-3 rounded border ${req.status === 'responded' ? 'bg-green-900/20 border-green-700' : 'bg-gray-700 border-gray-600'}`}>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <span className="text-white font-semibold">{req.name}</span>
                        <span className="text-gray-300 ml-2">({req.email})</span>
                        {req.company && <span className="text-gray-400 ml-2">- {req.company}</span>}
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs ${req.status === 'responded' ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'}`}>
                          {req.status}
                        </span>
                        {req.status === 'new' && (
                          <button
                            className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold hover:bg-blue-700"
                            onClick={() => markContactAsResponded(req.id, req.email)}
                          >
                            Respond
                          </button>
                        )}
                      </div>
                    </div>
                    <div className="text-gray-200 text-sm mb-2">
                      <strong>Message:</strong> {req.message}
                    </div>
                    <div className="text-gray-400 text-xs">
                      Submitted: {new Date(req.submitted_at).toLocaleString()}
                      {req.responded_at && (
                        <span className="ml-4">Responded: {new Date(req.responded_at).toLocaleString()}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Client Management Section */}
      <div className="mt-8">
        <h2 className="text-white text-base mb-2 font-bold">Client Management</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Client List */}
          <div className="lg:col-span-1">
            <h3 className="text-white text-sm mb-2 font-semibold">Active Clients</h3>
            <div className="bg-gray-800 rounded p-2 max-h-96 overflow-y-auto">
              {clients.length === 0 ? (
                <div className="text-gray-400">No active clients yet.</div>
              ) : (
                clients.map((client) => (
                  <div
                    key={client.id}
                    className={`mb-2 p-3 rounded cursor-pointer transition-colors ${
                      selectedClient?.id === client.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                    }`}
                    onClick={() => selectClient(client)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-semibold text-sm">{client.name}</div>
                        <div className="text-xs opacity-75">{client.username} • {client.email}</div>
                        <div className="text-xs opacity-60">{client.company}</div>
                      </div>
                      <div className="text-right">
                        {client.unreadMessages > 0 && (
                          <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full mb-1">
                            {client.unreadMessages} msg{client.unreadMessages !== 1 ? 's' : ''}
                          </div>
                        )}
                        {client.newFiles > 0 && (
                          <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                            {client.newFiles} file{client.newFiles !== 1 ? 's' : ''}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Selected Client Details */}
          <div className="lg:col-span-2">
            {selectedClient ? (
              <div>
                <h3 className="text-white text-sm mb-2 font-semibold">
                  {selectedClient.name} - Project Management
                </h3>

                {loadingClientData ? (
                  <div className="text-gray-400">Loading client data...</div>
                ) : (
                  <div className="space-y-4">
                    {/* Project Management */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-white text-xs font-medium">Projects ({projects.length})</h4>
                        <button
                          onClick={() => setShowCreateProject(true)}
                          className="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                        >
                          + New Project
                        </button>
                      </div>

                      {/* Create Project Form */}
                      {showCreateProject && (
                        <div className="mb-2 p-2 bg-gray-700 rounded">
                          <div className="space-y-2">
                            <input
                              type="text"
                              value={newProject.name}
                              onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="Project name..."
                              className="w-full px-2 py-1 rounded bg-gray-600 text-gray-100 text-xs"
                            />
                            <textarea
                              value={newProject.description}
                              onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                              placeholder="Project description (optional)..."
                              className="w-full px-2 py-1 rounded bg-gray-600 text-gray-100 text-xs"
                              rows={2}
                            />
                            <div className="flex gap-2">
                              <button
                                onClick={createProject}
                                className="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                              >
                                Create
                              </button>
                              <button
                                onClick={() => {
                                  setShowCreateProject(false);
                                  setNewProject({ name: '', description: '' });
                                }}
                                className="px-2 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-500"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Project List */}
                      <div className="bg-gray-800 rounded p-2 max-h-32 overflow-y-auto mb-2">
                        {loadingProjects ? (
                          <div className="text-gray-400 text-xs">Loading projects...</div>
                        ) : projects.length === 0 ? (
                          <div className="text-gray-400 text-xs">No projects yet. Create one to start.</div>
                        ) : (
                          projects.map((project) => (
                            <div
                              key={project.id}
                              className={`mb-1 p-2 rounded cursor-pointer transition-colors ${
                                selectedProject?.id === project.id
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                              }`}
                              onClick={() => {
                                setSelectedProject(project);
                                fetchProjectData(project);
                              }}
                            >
                              <div className="font-semibold text-xs">{project.name}</div>
                              {project.description && (
                                <div className="text-xs opacity-75">{project.description}</div>
                              )}
                              <div className="text-xs opacity-60">
                                Status: {project.status} • Created: {new Date(project.created_at).toLocaleDateString()}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>

                    {/* Messages & Files for Selected Project */}
                    {selectedProject ? (
                  <div className="space-y-4">
                    {/* Client Messages */}
                    <div>
                      <h4 className="text-white text-xs mb-2 font-medium">Messages ({clientMessages.length})</h4>
                      <div className="bg-gray-800 rounded p-4 max-h-96 overflow-y-auto overflow-x-hidden mb-2">
                        {clientMessages.length === 0 ? (
                          <div className="text-gray-400 text-xs">No messages from this client.</div>
                        ) : (
                          clientMessages.map((msg, i) => (
                            <div key={i} className={`mb-3 ${msg.sender_username === 'admin' ? 'text-right' : 'text-left'} relative`}>
                              <div className={`inline-block p-3 rounded max-w-xs ${
                                msg.sender_username === 'admin'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-700 text-gray-200'
                              }`}>
                                <div className="text-xs pr-6">{msg.content}</div>
                                <div className="text-[10px] opacity-75 mt-1">
                                  {msg.sender_username === 'admin' ? 'You' : selectedClient.name} • {new Date(msg.created_at).toLocaleString()}
                                </div>
                              </div>

                              {/* Dropdown Menu Button - In corner but visible */}
                              {!clientUser?.isTemporaryAccess && (
                                <div className={`absolute top-1 dropdown-container ${
                                  msg.sender_username === 'admin' ? 'left-1' : 'right-1'
                                }`}>
                                    <button
                                      onClick={() => setOpenMessageDropdown(openMessageDropdown === msg.id ? null : msg.id)}
                                      className="text-[10px] opacity-60 hover:opacity-100 p-0.5 hover:bg-gray-700 rounded"
                                    >
                                      ⋯
                                    </button>

                                    {/* Dropdown Menu */}
                                    {openMessageDropdown === msg.id && (
                                      <div className={`absolute ${
                                        msg.sender_username === 'admin' ? 'left-0' : 'right-0'
                                      } top-6 bg-gray-900 border border-gray-700 rounded-md shadow-xl z-[9999] py-0.5 min-w-[120px]`}>
                                        {(() => {
                                          const isWithin30Seconds = DeletionManager.isWithinErrorCorrectionWindow(msg.created_at);
                                          const isSentByAdmin = msg.sender_username === 'admin';

                                          if (isSentByAdmin && isWithin30Seconds) {
                                            // Within 30 seconds: only show "Remove for everyone"
                                            return (
                                              <>
                                                <button
                                                  onClick={() => {
                                                    copyMessage(msg.content);
                                                    setOpenMessageDropdown(null);
                                                  }}
                                                  className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                >
                                                  Copy
                                                </button>
                                                <button
                                                  onClick={() => {
                                                    deleteMessage(msg.id, true);
                                                    setOpenMessageDropdown(null);
                                                  }}
                                                  className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-red-300"
                                                >
                                                  Remove for everyone
                                                </button>
                                              </>
                                            );
                                          } else {
                                            // After 30 seconds or not sent by admin: show other options
                                            return (
                                              <>
                                                <button
                                                  onClick={() => {
                                                    copyMessage(msg.content);
                                                    setOpenMessageDropdown(null);
                                                  }}
                                                  className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                >
                                                  Copy
                                                </button>
                                                <button
                                                  onClick={() => {
                                                    deleteMessage(msg.id, false);
                                                    setOpenMessageDropdown(null);
                                                  }}
                                                  className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                >
                                                  Remove for me
                                                </button>
                                                <button
                                                  onClick={() => {
                                                    const reason = prompt('Reason for deletion request (optional):');
                                                    if (reason !== null) {
                                                      requestDeletion('message', msg.id, reason);
                                                    }
                                                    setOpenMessageDropdown(null);
                                                  }}
                                                  className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-blue-300"
                                                >
                                                  Request deletion
                                                </button>
                                              </>
                                            );
                                          }
                                        })()}
                                      </div>
                                    )}
                                  </div>
                                )}
                            </div>
                          ))
                        )}
                        <div ref={messagesEndRef} />
                      </div>

                      {/* Reply Input */}
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            onPaste={async (e) => {
                              // Handle file paste
                              const items = e.clipboardData?.items;
                              if (items) {
                                for (let i = 0; i < items.length; i++) {
                                  const item = items[i];
                                  if (item.kind === 'file') {
                                    e.preventDefault();
                                    const file = item.getAsFile();
                                    if (file) {
                                      setReplyAttachment(file);
                                      alert(`✅ File "${file.name}" pasted and ready to send!`);
                                    }
                                    break;
                                  }
                                }
                              }
                            }}
                            placeholder="Type your reply or paste a file..."
                            className="flex-1 px-2 py-1 rounded bg-gray-700 text-gray-100 text-xs"
                            onKeyPress={(e) => e.key === 'Enter' && sendReply()}
                          />
                          <input
                            type="file"
                            onChange={(e) => setReplyAttachment(e.target.files?.[0] || null)}
                            className="hidden"
                            id="reply-file-input"
                          />
                          <label
                            htmlFor="reply-file-input"
                            className="bg-gray-600 text-white px-3 py-1 rounded text-xs font-semibold cursor-pointer hover:bg-gray-700 transition"
                          >
                            Attach
                          </label>
                          <button
                            onClick={sendReply}
                            disabled={!replyContent.trim() && !replyAttachment}
                            className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold disabled:opacity-50"
                          >
                            Send
                          </button>
                        </div>
                        {replyAttachment && (
                          <div className="flex items-center justify-between bg-gray-700 rounded p-2">
                            <span className="text-gray-200 text-xs">📎 {replyAttachment.name} ({Math.round(replyAttachment.size / 1024)} KB)</span>
                            <button
                              type="button"
                              onClick={() => setReplyAttachment(null)}
                              className="text-red-400 hover:text-red-300 text-xs"
                            >
                              Remove
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Client Files */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-white text-xs font-medium">Files ({clientFiles.length})</h4>
                        <div className="flex items-center gap-2">
                          <input
                            type="file"
                            onChange={uploadFileToClient}
                            className="hidden"
                            id="admin-file-upload"
                            disabled={uploadingToClient}
                          />
                          <label
                            htmlFor="admin-file-upload"
                            className={`bg-green-600 text-white px-2 py-1 rounded text-xs font-semibold cursor-pointer ${
                              uploadingToClient ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-700'
                            }`}
                          >
                            {uploadingToClient ? 'Uploading...' : 'Upload to Client'}
                          </label>
                        </div>
                      </div>
                      <div className="bg-gray-800 rounded p-4 max-h-96 overflow-y-auto overflow-x-hidden">
                        {clientFiles.length === 0 ? (
                          <div className="text-gray-400 text-xs">No files from this client.</div>
                        ) : (
                          clientFiles.map((file, i) => (
                            <div key={i} className="mb-3 p-3 bg-gray-700 rounded relative pr-10">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <div className="flex items-center gap-2">
                                    <div className="text-gray-200 text-xs font-medium">{file.filename}</div>
                                    {file.uploaded_by_admin ? (
                                      <span className="bg-green-600 text-white text-xs px-2 py-1 rounded">You Uploaded</span>
                                    ) : (
                                      <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded">Client Upload</span>
                                    )}
                                  </div>
                                  <div className="text-gray-400 text-[10px]">
                                    {file.file_size ? `${Math.round(file.file_size / 1024)} KB` : ''} •
                                    {new Date(file.created_at).toLocaleString()} •
                                    {file.uploaded_by_admin ? 'Uploaded by You' : `Uploaded by ${selectedClient.name}`}
                                  </div>
                                </div>
                                <div className="flex gap-2 items-center">
                                  <a href={file.url} className="text-blue-400 underline text-xs" download>
                                    Download
                                  </a>

                                  {/* Dropdown Menu Button */}
                                  {!clientUser?.isTemporaryAccess && (
                                    <div className="absolute top-2 right-2 dropdown-container">
                                      <button
                                        onClick={() => setOpenFileDropdown(openFileDropdown === file.id ? null : file.id)}
                                        className="text-[10px] opacity-60 hover:opacity-100 p-0.5 hover:bg-gray-700 rounded"
                                      >
                                        ⋯
                                      </button>

                                      {/* Dropdown Menu */}
                                      {openFileDropdown === file.id && (
                                        <div className="absolute right-0 top-6 bg-gray-900 border border-gray-700 rounded-md shadow-xl z-[9999] py-0.5 min-w-[120px]">
                                          {(() => {
                                            const isWithin30Seconds = DeletionManager.isWithinErrorCorrectionWindow(file.created_at);
                                            const isUploadedByAdmin = file.uploaded_by_admin;

                                            if (isUploadedByAdmin && isWithin30Seconds) {
                                              // Within 30 seconds: only show "Remove for everyone"
                                              return (
                                                <>
                                                  <button
                                                    onClick={() => {
                                                      copyFile(file);
                                                      setOpenFileDropdown(null);
                                                    }}
                                                    className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                  >
                                                    Copy
                                                  </button>
                                                  <button
                                                    onClick={() => {
                                                      deleteFile(file.id, true);
                                                      setOpenFileDropdown(null);
                                                    }}
                                                    className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-red-300"
                                                  >
                                                    Remove for everyone
                                                  </button>
                                                </>
                                              );
                                            } else {
                                              // After 30 seconds or not uploaded by admin: show other options
                                              return (
                                                <>
                                                  <button
                                                    onClick={() => {
                                                      copyFile(file);
                                                      setOpenFileDropdown(null);
                                                    }}
                                                    className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                  >
                                                    Copy
                                                  </button>
                                                  <button
                                                    onClick={() => {
                                                      deleteFile(file.id, false);
                                                      setOpenFileDropdown(null);
                                                    }}
                                                    className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                                  >
                                                    Remove for me
                                                  </button>
                                                  <button
                                                    onClick={() => {
                                                      const reason = prompt('Reason for deletion request (optional):');
                                                      if (reason !== null) {
                                                        requestDeletion('file', file.id, reason);
                                                      }
                                                      setOpenFileDropdown(null);
                                                    }}
                                                    className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-blue-300"
                                                  >
                                                    Request deletion
                                                  </button>
                                                </>
                                              );
                                            }
                                          })()}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Existing Admin Comment */}
                              {file.admin_comment && (
                                <div className="mb-2 p-1 bg-gray-600 rounded text-xs">
                                  <span className="text-blue-400">Your comment:</span> {file.admin_comment}
                                </div>
                              )}

                              {/* Add/Edit Comment - Only for client uploads */}
                              {!file.uploaded_by_admin && (
                                <div className="flex gap-2">
                                  <input
                                    type="text"
                                    value={fileComments[file.id] || ''}
                                    onChange={(e) => setFileComments(prev => ({ ...prev, [file.id]: e.target.value }))}
                                    placeholder={file.admin_comment ? "Update comment..." : "Add comment..."}
                                    className="flex-1 px-2 py-1 rounded bg-gray-600 text-gray-100 text-xs"
                                    onKeyPress={(e) => e.key === 'Enter' && addFileComment(file.id)}
                                  />
                                  <button
                                    onClick={() => addFileComment(file.id)}
                                    disabled={!fileComments[file.id]?.trim()}
                                    className="bg-green-600 text-white px-2 py-1 rounded text-xs font-semibold disabled:opacity-50"
                                  >
                                    {file.admin_comment ? 'Update' : 'Comment'}
                                  </button>
                                </div>
                              )}
                            </div>
                          ))
                        )}
                        <div ref={filesEndRef} />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-400 text-center py-4">
                    <div className="text-xs">Select a project to view messages and files</div>
                  </div>
                )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-400 text-center py-8">
                <div className="text-sm">Select a client to view their projects</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Client Portal Testing Section */}
      <div className="mt-8">
        <h2 className="text-white text-base mb-2 font-bold">Client Portal Testing</h2>
        <p className="text-gray-400 text-xs mb-4">
          As an admin, you can test client accounts while maintaining your admin session.
          Navigate to the <a href="/client-portal" className="text-blue-400 underline">Client Portal</a> to login as any client for support purposes.
        </p>
        <div className="bg-gray-800 rounded p-4 space-y-4">
          {/* Active Client Sessions */}
          <div>
            <h3 className="text-white text-sm mb-2">Active Client Sessions:</h3>
            <div className="max-h-32 overflow-y-auto">
              {activeClientSessions.length === 0 ? (
                <div className="text-xs text-gray-400">No active client sessions</div>
              ) : (
                activeClientSessions.map((session, i) => (
                  <div key={i} className="flex items-center justify-between text-xs text-gray-300 mb-2 p-2 bg-gray-700 rounded">
                    <div className="flex-1">
                      <span className="font-mono bg-gray-600 px-2 py-1 rounded mr-2">{session.username}</span>
                      <span className="text-gray-400">{session.name} ({session.email})</span>
                    </div>
                    <div className="flex gap-1">
                      <button
                        onClick={() => switchToClientSession(session.username)}
                        className="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs"
                      >
                        Switch
                      </button>
                      <button
                        onClick={() => logoutClientSession(session.username)}
                        className="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-xs"
                      >
                        End
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Temporary Access Grants */}
          <div>
            <h3 className="text-white text-sm mb-2">🔑 Temporary Access Grants:</h3>
            <div className="max-h-32 overflow-y-auto">
              {temporaryAccess.length === 0 ? (
                <div className="text-xs text-gray-400">No temporary access grants</div>
              ) : (
                temporaryAccess.map((access, i) => (
                  <div key={i} className="flex items-center justify-between text-xs text-gray-300 mb-2 p-2 bg-green-900/30 border border-green-600/30 rounded">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-mono bg-green-700 px-2 py-1 rounded text-green-100">{access.client_username}</span>
                        <span className="text-green-300">⏰ {access.access_duration_days}d</span>
                      </div>
                      <div className="text-gray-400 mt-1">
                        Expires: {new Date(access.expires_at).toLocaleDateString()} at {new Date(access.expires_at).toLocaleTimeString()}
                      </div>
                      {access.last_used_at && (
                        <div className="text-gray-500 text-xs">
                          Last used: {new Date(access.last_used_at).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => useTemporaryAccess(access)}
                        className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-xs font-medium"
                      >
                        Access Account
                      </button>
                      <button
                        onClick={() => revokeTemporaryAccessAsAdmin(access)}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-xs font-medium"
                        title="Revoke this access grant"
                      >
                        Revoke Access
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="text-xs text-gray-400 border-t border-gray-700 pt-2">
            💡 Login to client accounts via the <a href="/client-portal" className="text-blue-400 underline">Client Portal</a> to add them to Active Sessions.
            <br />
            🔑 Clients can grant temporary access from their portal's "Admin Access" section.
          </div>
        </div>
      </div>

      {/* Reason Popup Modal */}
      {showReasonPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-white text-lg font-bold mb-4">Deletion Request Reason</h3>
            <div className="bg-gray-700 p-3 rounded mb-4">
              <p className="text-gray-200 text-sm whitespace-pre-wrap">
                {selectedReason || 'No reason provided'}
              </p>
            </div>
            <button
              onClick={() => {
                setShowReasonPopup(false);
                setSelectedReason('');
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default CompanyPortal;
