# Contact Form Testing Guide

## Prerequisites
1. Ensure the `contact_requests` table has been created in Supabase (run the SQL script from `add_contact_requests_table.sql`)
2. Verify the .env file has the correct Supabase URL and anon key
3. Development server is running (`npm run dev`)

## Test Steps

### 1. Test Contact Form Submission
1. Navigate to `http://localhost:5173/` 
2. Go to the Contact page
3. Fill out the contact form with test data:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Company: "Test Company"
   - Message: "This is a test message from the contact form"
4. Click "Send Message"
5. Verify you see a success message: "Message sent! Thank you for reaching out. We will get back to you soon."

### 2. Verify Database Storage
1. Go to Supabase dashboard → Table Editor
2. Select the `contact_requests` table
3. Verify the test submission appears with:
   - Correct name, email, company, message
   - Status: "new"
   - submitted_at timestamp
   - All other fields should be null

### 3. Test Admin Portal Display
1. Navigate to `http://localhost:5173/company-portal` (or wherever the admin portal is accessible)
2. Verify the "Contact Form Submissions" section shows the test submission
3. Verify the submission displays:
   - Name, email, company
   - Full message text
   - Status badge showing "new"
   - Submitted timestamp
   - "Respond" button

### 4. Test Admin Response Flow
1. Click the "Respond" button on a contact request
2. Verify:
   - Status changes to "responded"
   - Email client opens with pre-filled recipient
   - Database record updates with responded_at timestamp and responded_by: "admin"

### 5. Test Error Handling
1. Try submitting the contact form without required fields
2. Verify proper validation messages appear
3. If Supabase is unreachable, verify error message displays properly

## Expected Results
- ✅ Contact form submissions save to `contact_requests` table
- ✅ Admin portal displays contact requests alongside access requests
- ✅ Admin can mark requests as responded
- ✅ Proper error handling for validation and network issues
- ✅ No more "user requests aren't showing up" issue

## Troubleshooting
- If submissions don't appear: Check browser console for errors
- If database errors: Verify Supabase credentials and table exists
- If admin portal doesn't load contact requests: Check network tab for API errors
