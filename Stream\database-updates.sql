-- Safe database updates - only add columns if they don't exist

-- Add columns to messages table (safe approach)
DO $$
BEGIN
    -- Add read_by_admin column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='read_by_admin') THEN
        ALTER TABLE messages ADD COLUMN read_by_admin BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add sender_username column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='sender_username') THEN
        ALTER TABLE messages ADD COLUMN sender_username VARCHAR(255);
    END IF;

    -- Add recipient_username column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='recipient_username') THEN
        ALTER TABLE messages ADD COLUMN recipient_username VARCHAR(255);
    END IF;

    -- Add read_by_client column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='read_by_client') THEN
        ALTER TABLE messages ADD COLUMN read_by_client BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add created_at column if it doesn't exist (rename from sent_at)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='created_at') THEN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='messages' AND column_name='sent_at') THEN
            ALTER TABLE messages RENAME COLUMN sent_at TO created_at;
        ELSE
            ALTER TABLE messages ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
        END IF;
    END IF;
END $$;

-- Add columns to files table (safe approach)
DO $$
BEGIN
    -- Add reviewed_by_admin column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='reviewed_by_admin') THEN
        ALTER TABLE files ADD COLUMN reviewed_by_admin BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add admin_comment column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='admin_comment') THEN
        ALTER TABLE files ADD COLUMN admin_comment TEXT;
    END IF;

    -- Add file_size column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='file_size') THEN
        ALTER TABLE files ADD COLUMN file_size INTEGER;
    END IF;

    -- Add file_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='file_type') THEN
        ALTER TABLE files ADD COLUMN file_type VARCHAR(255);
    END IF;

    -- Add uploaded_by_admin column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='uploaded_by_admin') THEN
        ALTER TABLE files ADD COLUMN uploaded_by_admin BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add target_username column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='target_username') THEN
        ALTER TABLE files ADD COLUMN target_username VARCHAR(255);
    END IF;

    -- Add read_by_client column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='read_by_client') THEN
        ALTER TABLE files ADD COLUMN read_by_client BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add created_at column if it doesn't exist (rename from uploaded_at)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='created_at') THEN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='files' AND column_name='uploaded_at') THEN
            ALTER TABLE files RENAME COLUMN uploaded_at TO created_at;
        ELSE
            ALTER TABLE files ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
        END IF;
    END IF;
END $$;

-- Create indexes for better performance (safe - IF NOT EXISTS)
CREATE INDEX IF NOT EXISTS idx_messages_read_by_admin ON messages(read_by_admin);
CREATE INDEX IF NOT EXISTS idx_messages_sender_username ON messages(sender_username);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_username ON messages(recipient_username);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_reviewed_by_admin ON files(reviewed_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_uploader_username ON files(uploader_username);
CREATE INDEX IF NOT EXISTS idx_files_target_username ON files(target_username);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by_admin ON files(uploaded_by_admin);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at DESC);

-- Update existing records to have proper default values
UPDATE messages SET read_by_admin = FALSE WHERE read_by_admin IS NULL;
UPDATE messages SET read_by_client = FALSE WHERE read_by_client IS NULL;
UPDATE files SET reviewed_by_admin = FALSE WHERE reviewed_by_admin IS NULL;
UPDATE files SET uploaded_by_admin = FALSE WHERE uploaded_by_admin IS NULL;
UPDATE files SET read_by_client = FALSE WHERE read_by_client IS NULL;
