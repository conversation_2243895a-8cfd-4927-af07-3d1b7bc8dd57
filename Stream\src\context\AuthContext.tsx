// Enhanced auth context to manage both client and admin sessions
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabase } from '../lib/supabaseClient';

type User = {
  id: string;
  username: string;
  email: string;
  name: string;
  company: string;
  type: 'client' | 'admin';
  isTemporaryAccess?: boolean;
  temporaryAccessId?: string;
  adminSessionStartTime?: string;
};

type AuthContextType = {
  user: User | null;
  adminUser: User | null;
  clientUser: User | null;
  activeClientSessions: User[]; // Multiple client sessions for admin
  loading: boolean;
  loginAdmin: (userData: User) => void;
  loginClient: (userData: User) => void;
  switchToClientSession: (username: string) => void; // Switch between client sessions
  logoutAdmin: () => void;
  logoutClient: () => void;
  logoutClientSession: (username: string) => void; // Logout specific client session
  endTemporaryAccess: (accessId: string) => Promise<void>; // End admin's temporary access without logging out client
  login: (userData: User) => void; // Legacy support
  logout: () => void; // Legacy support
  isAuthenticated: boolean;
  isAdmin: boolean;
  isClient: boolean;
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  adminUser: null,
  clientUser: null,
  activeClientSessions: [],
  loading: true,
  loginAdmin: () => {},
  loginClient: () => {},
  switchToClientSession: () => {},
  logoutAdmin: () => {},
  logoutClient: () => {},
  logoutClientSession: () => {},
  endTemporaryAccess: async () => {},
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
  isAdmin: false,
  isClient: false
});

type AuthProviderProps = {
  children: ReactNode;
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [adminUser, setAdminUser] = useState<User | null>(null);
  const [clientUser, setClientUser] = useState<User | null>(null);
  const [activeClientSessions, setActiveClientSessions] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  // Validate session against database
  const validateSessionAgainstDatabase = async (user: User): Promise<boolean> => {
    try {
      if (user.type === 'admin') {
        // For admin, just check if they exist (admin accounts are not in users table)
        return true;
      } else {
        // For clients, check if they still exist in database
        const { data: userData, error } = await supabase
          .from('users')
          .select('username, is_active')
          .eq('username', user.username)
          .single();

        if (error || !userData || !userData.is_active) {
          console.log(`❌ User ${user.username} no longer exists in database`);
          return false;
        }
        return true;
      }
    } catch (error) {
      console.error('❌ Error validating session against database:', error);
      return false;
    }
  };

  // Load sessions from localStorage on app start
  useEffect(() => {
    const loadSession = async (key: string, setter: (user: User | null) => void) => {
      console.log(`🔍 Loading session from ${key}...`);
      const savedSession = localStorage.getItem(key);

      if (savedSession) {
        try {
          const userData = JSON.parse(savedSession);
          console.log(`📦 Found session data for ${key}:`, userData);

          // Validate session hasn't expired (24 hours)
          const sessionTime = new Date(userData.sessionTime);
          const now = new Date();
          const hoursDiff = (now.getTime() - sessionTime.getTime()) / (1000 * 60 * 60);

          console.log(`⏰ Session age: ${hoursDiff.toFixed(2)} hours`);

          if (hoursDiff < 24) {
            // Validate against database
            const isValid = await validateSessionAgainstDatabase(userData.user);
            if (isValid) {
              console.log(`✅ Session valid, restoring user for ${key}`);
              setter(userData.user);
            } else {
              console.log(`❌ Session invalid in database for ${key}, clearing...`);
              localStorage.removeItem(key);
            }
          } else {
            console.log(`❌ Session expired for ${key}, clearing...`);
            localStorage.removeItem(key);
          }
        } catch (error) {
          console.error(`❌ Error loading session ${key}:`, error);
          localStorage.removeItem(key);
        }
      } else {
        console.log(`📭 No session found for ${key}`);
      }
    };

    const initializeSessions = async () => {
      await loadSession('daswos_admin_session', setAdminUser);
      await loadSession('daswos_client_session', setClientUser);

      // Load and validate active client sessions
      const savedActiveSessions = localStorage.getItem('daswos_active_client_sessions');
      if (savedActiveSessions) {
        try {
          const sessions = JSON.parse(savedActiveSessions);
          console.log('📦 Loading active client sessions:', sessions);

          // Validate each session against database
          const validSessions = [];
          for (const session of sessions) {
            const isValid = await validateSessionAgainstDatabase(session);
            if (isValid) {
              validSessions.push(session);
            } else {
              console.log(`❌ Removing invalid session for ${session.username}`);
            }
          }

          setActiveClientSessions(validSessions);

          // Update localStorage with only valid sessions
          if (validSessions.length !== sessions.length) {
            localStorage.setItem('daswos_active_client_sessions', JSON.stringify(validSessions));
          }
        } catch (error) {
          console.error('❌ Error loading active client sessions:', error);
          localStorage.removeItem('daswos_active_client_sessions');
        }
      }

      setLoading(false);
    };

    initializeSessions();
  }, []);

  // Persist sessions on every change (backup mechanism)
  useEffect(() => {
    if (adminUser) {
      console.log('💾 Persisting admin session...');
      saveSession('daswos_admin_session', adminUser);
    }
  }, [adminUser]);

  useEffect(() => {
    if (clientUser) {
      console.log('💾 Persisting client session...');
      saveSession('daswos_client_session', clientUser);
    }
  }, [clientUser]);

  const saveSession = (key: string, userData: User) => {
    try {
      const sessionData = {
        user: userData,
        sessionTime: new Date().toISOString()
      };
      localStorage.setItem(key, JSON.stringify(sessionData));
      console.log(`✅ Session saved for ${key}:`, userData.username);

      // Also save to sessionStorage as backup
      sessionStorage.setItem(key + '_backup', JSON.stringify(sessionData));
    } catch (error) {
      console.error(`❌ Failed to save session for ${key}:`, error);
    }
  };

  // Refresh session timestamp (extend session)
  const refreshSession = (userType: 'admin' | 'client') => {
    const key = userType === 'admin' ? 'daswos_admin_session' : 'daswos_client_session';
    const user = userType === 'admin' ? adminUser : clientUser;

    if (user) {
      console.log(`🔄 Refreshing ${userType} session...`);
      saveSession(key, user);
    }
  };

  const loginAdmin = (userData: User) => {
    setAdminUser(userData);
    saveSession('daswos_admin_session', userData);
  };

  const loginClient = (userData: User) => {
    setClientUser(userData);
    saveSession('daswos_client_session', userData);

    // Add to active client sessions if not already there
    setActiveClientSessions(prev => {
      const existing = prev.find(u => u.username === userData.username);
      if (!existing) {
        const newSessions = [...prev, userData];
        // Save active sessions to localStorage
        localStorage.setItem('daswos_active_client_sessions', JSON.stringify(newSessions));
        return newSessions;
      }
      return prev;
    });
  };

  // Switch to an existing client session
  const switchToClientSession = (username: string) => {
    const session = activeClientSessions.find(u => u.username === username);
    if (session) {
      console.log(`🔄 Switching to client session: ${username}`);
      setClientUser(session);
      saveSession('daswos_client_session', session);
    }
  };

  // Logout specific client session
  const logoutClientSession = (username: string) => {
    console.log(`🚪 Logging out client session: ${username}`);

    // Remove from active sessions
    setActiveClientSessions(prev => {
      const newSessions = prev.filter(u => u.username !== username);
      localStorage.setItem('daswos_active_client_sessions', JSON.stringify(newSessions));
      return newSessions;
    });

    // If this was the current client, clear it
    if (clientUser?.username === username) {
      setClientUser(null);
      localStorage.removeItem('daswos_client_session');
    }
  };

  // End admin's temporary access without logging out the real client
  const endTemporaryAccess = async (accessId: string) => {
    try {
      console.log(`🔚 Ending temporary access for ID: ${accessId}`);

      const { createClient } = await import('../lib/supabaseClient');
      const supabase = createClient();

      // First, get the session details before updating
      const { data: sessionData, error: fetchError } = await supabase
        .from('temporary_access')
        .select('client_username, admin_session_started_at')
        .eq('id', accessId)
        .single();

      if (fetchError || !sessionData) {
        console.error('❌ Failed to fetch session details:', fetchError);
        throw new Error('Could not fetch session details');
      }

      // Get client's email for notification
      const { data: clientData, error: clientError } = await supabase
        .from('users')
        .select('email')
        .eq('username', sessionData.client_username)
        .single();

      if (clientError || !clientData) {
        console.error('❌ Failed to fetch client email:', clientError);
        // Continue with ending access even if email fails
      }

      const endTime = new Date().toISOString();

      // Update database to mark admin as no longer accessing
      const { error } = await supabase
        .from('temporary_access')
        .update({
          admin_currently_accessing: false,
          admin_session_ended_at: endTime
        })
        .eq('id', accessId);

      if (error) {
        console.error('❌ Failed to end temporary access:', error);
        throw error;
      }

      console.log('✅ Temporary access ended successfully');

      // Get admin activity logs for this session and send email notification
      if (clientData?.email && sessionData.admin_session_started_at) {
        try {
          const { adminActivityLogger } = await import('../utils/adminActivityLogger');
          const activities = await adminActivityLogger.getSessionActivities(accessId);
          const activitySummary = adminActivityLogger.formatActivitiesForEmail(activities);

          const emailService = await import('../utils/emailService');
          await emailService.default.sendAdminAccessEndedEmail(
            clientData.email,
            sessionData.client_username,
            sessionData.admin_session_started_at,
            endTime,
            adminUser?.username || 'Admin',
            'Admin logged out',
            activitySummary
          );
          console.log('✅ Admin access ended email sent to client with activity summary');

          // Clear the activity logger session
          adminActivityLogger.clearSession();
        } catch (emailError) {
          console.error('❌ Failed to send admin access ended email:', emailError);
          // Don't block the logout if email fails
        }
      }

      // Only remove the admin's temporary session, don't affect the real client
      // The real client should remain logged in to their own account

    } catch (error) {
      console.error('❌ Error ending temporary access:', error);
      throw error;
    }
  };

  const logoutAdmin = () => {
    setAdminUser(null);
    localStorage.removeItem('daswos_admin_session');
  };

  const logoutClient = async () => {
    // If this was a temporary access session, mark it as no longer being accessed
    if (clientUser?.isTemporaryAccess && clientUser?.temporaryAccessId) {
      try {
        const { createClient } = await import('../lib/supabaseClient');
        const supabase = createClient();

        await supabase
          .from('temporary_access')
          .update({
            admin_currently_accessing: false,
            admin_session_started_at: null
          })
          .eq('id', clientUser.temporaryAccessId);

        console.log('🔓 Marked temporary access as no longer being used');
      } catch (error) {
        console.error('Error updating temporary access status:', error);
      }
    }

    setClientUser(null);
    localStorage.removeItem('daswos_client_session');
  };

  // Legacy support - defaults to client behavior
  const login = (userData: User) => {
    if (userData.type === 'admin') {
      loginAdmin(userData);
    } else {
      loginClient(userData);
    }
  };

  const logout = () => {
    logoutClient();
  };

  // Current user is admin if admin session exists, otherwise client
  const user = adminUser || clientUser;
  const isAuthenticated = user !== null;
  const isAdmin = adminUser !== null;
  const isClient = clientUser !== null;

  return (
    <AuthContext.Provider value={{
      user,
      adminUser,
      clientUser,
      activeClientSessions,
      loading,
      loginAdmin,
      loginClient,
      switchToClientSession,
      logoutAdmin,
      logoutClient,
      logoutClientSession,
      endTemporaryAccess,
      login,
      logout,
      isAuthenticated,
      isAdmin,
      isClient
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
