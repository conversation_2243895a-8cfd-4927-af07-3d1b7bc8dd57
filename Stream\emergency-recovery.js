// EMERGENCY DATA RECOVERY SCRIPT
// Copy and paste this entire script into your browser console

(async function emergencyRecovery() {
  console.log('🚨 EMERGENCY DATA RECOVERY STARTED');

  // Initialize Supabase client
  const { createClient } = window.supabase || {};
  if (!createClient) {
    console.error('❌ Supabase not available. Make sure you are on the deployed site.');
    return;
  }

  const supabase = createClient(
    'https://hnpqkzqxjqjqkzqxjqjq.supabase.co', // Replace with your actual URL
    'your-anon-key' // Replace with your actual anon key
  );

  // Function to safely insert data
  const safeInsert = async (table, data) => {
    try {
      const { error } = await supabase.from(table).insert([data]);
      if (error) {
        console.warn(`Failed to insert into ${table}:`, error.message);
        return false;
      }
      return true;
    } catch (err) {
      console.warn(`Error inserting into ${table}:`, err);
      return false;
    }
  };

  // Recover access requests
  const recoverAccessRequests = () => {
    const keys = ['daswos_access_requests', 'cached_access_requests'];
    let recovered = 0;

    for (const key of keys) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          const items = Array.isArray(parsed) ? parsed : [parsed];

          for (const item of items) {
            if (item && item.email) {
              safeInsert('access_requests', {
                id: item.id || crypto.randomUUID(),
                name: item.name,
                email: item.email,
                company: item.company,
                status: item.status || 'pending',
                verification_token: item.verification_token,
                token_expires_at: item.token_expires_at,
                submitted_at: item.submitted_at || new Date().toISOString()
              });
              recovered++;
            }
          }
        } catch (e) {
          console.warn(`Failed to parse ${key}:`, e);
        }
      }
    }

    console.log(`📦 Access Requests: ${recovered} items recovered`);
    return recovered;
  };

  // Recover messages
  const recoverMessages = () => {
    const keys = ['daswos_messages', 'cached_messages'];
    let recovered = 0;

    for (const key of keys) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          const items = Array.isArray(parsed) ? parsed : [parsed];

          for (const item of items) {
            if (item && item.content) {
              safeInsert('messages', {
                id: item.id || crypto.randomUUID(),
                sender_username: item.sender_username,
                recipient_username: item.recipient_username,
                content: item.content,
                read_by_admin: item.read_by_admin || false,
                read_by_client: item.read_by_client || false,
                created_at: item.created_at || new Date().toISOString()
              });
              recovered++;
            }
          }
        } catch (e) {
          console.warn(`Failed to parse ${key}:`, e);
        }
      }
    }

    console.log(`💬 Messages: ${recovered} items recovered`);
    return recovered;
  };

  // Recover files
  const recoverFiles = () => {
    const keys = ['daswos_files', 'cached_files'];
    let recovered = 0;

    for (const key of keys) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          const items = Array.isArray(parsed) ? parsed : [parsed];

          for (const item of items) {
            if (item && item.filename) {
              safeInsert('files', {
                id: item.id || crypto.randomUUID(),
                uploader_username: item.uploader_username,
                filename: item.filename,
                file_size: item.file_size,
                file_type: item.file_type,
                url: item.url,
                reviewed_by_admin: item.reviewed_by_admin || false,
                uploaded_by_admin: item.uploaded_by_admin || false,
                target_username: item.target_username,
                read_by_client: item.read_by_client || false,
                created_at: item.created_at || new Date().toISOString()
              });
              recovered++;
            }
          }
        } catch (e) {
          console.warn(`Failed to parse ${key}:`, e);
        }
      }
    }

    console.log(`📁 Files: ${recovered} items recovered`);
    return recovered;
  };

  // Run recovery
  const totalAccessRequests = recoverAccessRequests();
  const totalMessages = recoverMessages();
  const totalFiles = recoverFiles();

  const total = totalAccessRequests + totalMessages + totalFiles;

  console.log(`🎯 RECOVERY COMPLETE: ${total} total items recovered`);
  console.log('✅ Refresh the page to see recovered data');

  return { totalAccessRequests, totalMessages, totalFiles, total };
})();