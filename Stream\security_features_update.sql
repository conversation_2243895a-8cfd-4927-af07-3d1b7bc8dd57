-- Security Features Update Script
-- Run this script in your Supabase SQL Editor to add all security enhancements

-- 1. Add email verification columns to access_requests table
ALTER TABLE access_requests 
ADD COLUMN IF NOT EXISTS email_verification_token text,
ADD COLUMN IF NOT EXISTS email_verified_at timestamptz,
ADD COLUMN IF NOT EXISTS verification_expires_at timestamptz;

-- 2. Update default status to 'unverified' for new requests
-- Note: This will only affect new inserts, existing records remain unchanged
ALTER TABLE access_requests 
ALTER COLUMN status SET DEFAULT 'unverified';

-- 3. Create password_reset_tokens table for secure password resets
CREATE TABLE IF NOT EXISTS password_reset_tokens (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token text NOT NULL UNIQUE,
  expires_at timestamptz NOT NULL,
  used_at timestamptz NULL,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- 4. Create login_attempts table for rate limiting tracking
CREATE TABLE IF NOT EXISTS login_attempts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  identifier text NOT NULL, -- IP address or username
  attempt_type text NOT NULL, -- 'login', 'access_request', 'password_reset'
  success boolean NOT NULL DEFAULT false,
  attempted_at timestamptz NOT NULL DEFAULT now(),
  ip_address text,
  user_agent text
);

-- 5. Create email_verification_logs table for tracking
CREATE TABLE IF NOT EXISTS email_verification_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  access_request_id uuid REFERENCES access_requests(id) ON DELETE CASCADE,
  email text NOT NULL,
  verification_token text NOT NULL,
  sent_at timestamptz NOT NULL DEFAULT now(),
  verified_at timestamptz NULL,
  expires_at timestamptz NOT NULL
);

-- 6. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_access_requests_verification_token ON access_requests(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_access_requests_email_verified ON access_requests(email_verified_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON password_reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires ON password_reset_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_login_attempts_identifier ON login_attempts(identifier);
CREATE INDEX IF NOT EXISTS idx_login_attempts_attempted_at ON login_attempts(attempted_at);
CREATE INDEX IF NOT EXISTS idx_email_verification_logs_token ON email_verification_logs(verification_token);

-- 7. Create function to clean up expired tokens (optional)
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS void AS $$
BEGIN
  -- Clean up expired password reset tokens
  DELETE FROM password_reset_tokens 
  WHERE expires_at < now() AND used_at IS NULL;
  
  -- Clean up old login attempts (keep last 30 days)
  DELETE FROM login_attempts 
  WHERE attempted_at < now() - INTERVAL '30 days';
  
  -- Clean up old verification logs (keep last 90 days)
  DELETE FROM email_verification_logs 
  WHERE sent_at < now() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- 8. Create function to check rate limits (optional)
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_identifier text,
  p_attempt_type text,
  p_max_attempts integer DEFAULT 5,
  p_window_minutes integer DEFAULT 15
)
RETURNS boolean AS $$
DECLARE
  attempt_count integer;
BEGIN
  -- Count attempts within the time window
  SELECT COUNT(*) INTO attempt_count
  FROM login_attempts
  WHERE identifier = p_identifier
    AND attempt_type = p_attempt_type
    AND attempted_at > now() - (p_window_minutes || ' minutes')::interval;
  
  -- Return true if rate limit exceeded
  RETURN attempt_count >= p_max_attempts;
END;
$$ LANGUAGE plpgsql;

-- 9. Update existing plain text passwords to hashed (MANUAL STEP REQUIRED)
-- WARNING: This is a one-time migration. Run carefully!
-- You'll need to update this with actual bcrypt hashes

-- Example for demo user (replace with actual hashed password):
-- UPDATE users 
-- SET password_hash = '$2a$12$...' -- Replace with actual bcrypt hash of 'SODA'
-- WHERE username = 'demo_user' AND password_hash = 'SODA';

-- 10. Add constraints for data integrity
ALTER TABLE access_requests 
ADD CONSTRAINT check_status_values 
CHECK (status IN ('unverified', 'pending', 'approved', 'rejected'));

-- 11. Add RLS (Row Level Security) policies if needed
-- Enable RLS on sensitive tables
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE access_requests ENABLE ROW LEVEL SECURITY;

-- Create policies (example - adjust based on your needs)
-- CREATE POLICY "Users can only see their own data" ON users
--   FOR ALL USING (auth.uid() = id);

-- 12. Create audit log table for security events
CREATE TABLE IF NOT EXISTS security_audit_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type text NOT NULL, -- 'login_success', 'login_failure', 'password_reset', etc.
  user_id uuid REFERENCES users(id),
  username text,
  email text,
  ip_address text,
  user_agent text,
  details jsonb,
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_security_audit_log_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_created_at ON security_audit_log(created_at);

-- 13. Insert some sample data for testing (optional)
-- INSERT INTO login_attempts (identifier, attempt_type, success, ip_address)
-- VALUES ('<EMAIL>', 'login', false, '127.0.0.1');

COMMENT ON TABLE password_reset_tokens IS 'Stores secure password reset tokens with expiration';
COMMENT ON TABLE login_attempts IS 'Tracks login attempts for rate limiting and security monitoring';
COMMENT ON TABLE email_verification_logs IS 'Logs email verification attempts and status';
COMMENT ON TABLE security_audit_log IS 'Comprehensive security event logging';

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Security features update completed successfully!';
  RAISE NOTICE 'Remember to:';
  RAISE NOTICE '1. Update existing plain text passwords to hashed versions';
  RAISE NOTICE '2. Configure RLS policies if needed';
  RAISE NOTICE '3. Set up automated cleanup jobs for expired tokens';
  RAISE NOTICE '4. Monitor the security_audit_log table for suspicious activity';
END $$;
